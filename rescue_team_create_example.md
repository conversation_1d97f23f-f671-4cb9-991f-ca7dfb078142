# 救援队伍新增接口完整示例

## 接口说明

新增救援队伍时，系统会同时创建队伍专属的物资和装备，而不是从现有物资中选择关联。

## 请求示例

### HTTP请求
```http
POST /rescue/team
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "teamName": "南宁市消防救援第一队",
  "teamCode": "NNXF001",
  "address": "南宁市青秀区民族大道100号消防大队",
  "longitude": 108.366543,
  "latitude": 22.817002,
  "teamSize": 45,
  "leaderName": "张志强",
  "leaderPhone": "13800138001",
  "jurisdictionUnit": "南宁市消防救援支队",
  "jurisdictionLeader": "李建国",
  "jurisdictionPhone": "13900139001",
  "teamType": "1",
  "specialties": "火灾救援,地震救援,水域救援,高空救援",
  "status": 1,
  "remark": "专业消防救援队伍，配备先进救援设备，24小时待命",
  "materials": [
    {
      "materialName": "医疗急救包",
      "specModel": "标准型",
      "categoryCode": "MEDICAL",
      "quantity": 50,
      "unit": "个",
      "expiryDate": "2025-12-31",
      "remark": "包含基础医疗用品"
    },
    {
      "materialName": "应急食品",
      "specModel": "压缩饼干",
      "categoryCode": "FOOD",
      "quantity": 200,
      "unit": "包",
      "expiryDate": "2025-06-30",
      "remark": "高能量压缩食品"
    },
    {
      "materialName": "应急药品",
      "specModel": "常用药品包",
      "categoryCode": "MEDICAL",
      "quantity": 30,
      "unit": "盒",
      "expiryDate": "2025-08-31",
      "remark": "包含常用急救药品"
    }
  ],
  "equipments": [
    {
      "materialName": "消防水枪",
      "specModel": "QZ19/6.5",
      "categoryCode": "FIRE_EQUIP",
      "quantity": 10,
      "unit": "支",
      "remark": "高压消防水枪"
    },
    {
      "materialName": "救生绳索",
      "specModel": "直径12mm",
      "categoryCode": "RESCUE_EQUIP",
      "quantity": 20,
      "unit": "根",
      "remark": "高强度救援绳索，长度50米"
    },
    {
      "materialName": "防护服",
      "specModel": "阻燃防护服",
      "categoryCode": "PROTECT_EQUIP",
      "quantity": 50,
      "unit": "套",
      "remark": "高温阻燃防护服"
    },
    {
      "materialName": "呼吸器",
      "specModel": "正压式空气呼吸器",
      "categoryCode": "PROTECT_EQUIP",
      "quantity": 25,
      "unit": "台",
      "remark": "6.8L碳纤维气瓶"
    }
  ]
}
```

## 响应示例

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 失败响应示例

#### 队伍编号重复
```json
{
  "code": 500,
  "msg": "新增救援队伍'南宁市消防救援第一队'失败，队伍编号已存在",
  "data": null
}
```

#### 参数验证失败
```json
{
  "code": 400,
  "msg": "参数错误",
  "data": {
    "teamName": "队伍名称不能为空",
    "teamSize": "队伍人数必须大于0",
    "materials[0].materialName": "物资名称不能为空",
    "equipments[0].quantity": "装备数量必须大于0"
  }
}
```

## 业务逻辑说明

### 1. 数据创建流程
1. **创建救援队伍记录**：在 `rescue_team` 表中插入队伍基础信息
2. **创建专属物资**：遍历 `materials` 数组，为每个物资创建记录
   - 在 `em_material` 表中插入物资记录（`material_type = '0'`）
   - 设置 `team_id` 字段标识归属队伍
   - 在 `rescue_team_material` 表中创建关联关系
3. **创建专属装备**：遍历 `equipments` 数组，为每个装备创建记录
   - 在 `em_material` 表中插入装备记录（`material_type = '1'`）
   - 设置 `team_id` 字段标识归属队伍
   - 在 `rescue_team_material` 表中创建关联关系

### 2. 字段自动设置
- **队伍ID**：系统自动生成UUID
- **物资/装备ID**：系统自动生成UUID
- **物资类型**：materials数组中的项目自动设置为'0'，equipments数组中的项目自动设置为'1'
- **状态**：默认设置为1（正常）
- **创建信息**：自动设置创建人和创建时间
- **删除标志**：默认设置为0（未删除）

### 3. 事务保证
整个创建过程在一个事务中执行，确保数据一致性：
- 如果队伍创建失败，不会创建任何物资和装备
- 如果物资或装备创建失败，整个操作回滚

## 前端调用示例

### JavaScript/Axios
```javascript
async function createRescueTeamWithMaterials() {
  const teamData = {
    teamName: "南宁市消防救援第一队",
    teamCode: "NNXF001",
    address: "南宁市青秀区民族大道100号消防大队",
    longitude: 108.366543,
    latitude: 22.817002,
    teamSize: 45,
    leaderName: "张志强",
    leaderPhone: "13800138001",
    jurisdictionUnit: "南宁市消防救援支队",
    jurisdictionLeader: "李建国",
    jurisdictionPhone: "13900139001",
    teamType: "1",
    specialties: "火灾救援,地震救援,水域救援,高空救援",
    status: 1,
    remark: "专业消防救援队伍，配备先进救援设备，24小时待命",
    materials: [
      {
        materialName: "医疗急救包",
        specModel: "标准型",
        categoryCode: "MEDICAL",
        quantity: 50,
        unit: "个",
        expiryDate: "2025-12-31",
        remark: "包含基础医疗用品"
      },
      {
        materialName: "应急食品",
        specModel: "压缩饼干",
        categoryCode: "FOOD",
        quantity: 200,
        unit: "包",
        expiryDate: "2025-06-30",
        remark: "高能量压缩食品"
      }
    ],
    equipments: [
      {
        materialName: "消防水枪",
        specModel: "QZ19/6.5",
        categoryCode: "FIRE_EQUIP",
        quantity: 10,
        unit: "支",
        remark: "高压消防水枪"
      },
      {
        materialName: "救生绳索",
        specModel: "直径12mm",
        categoryCode: "RESCUE_EQUIP",
        quantity: 20,
        unit: "根",
        remark: "高强度救援绳索，长度50米"
      }
    ]
  };

  try {
    const response = await axios.post('/rescue/team', teamData);
    if (response.data.code === 200) {
      console.log('救援队伍及其物资装备创建成功');
      // 可以跳转到队伍详情页面或刷新列表
      return true;
    } else {
      console.error('创建失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return false;
  }
}
```

### Vue.js表单提交
```vue
<template>
  <el-form :model="teamForm" :rules="rules" ref="teamForm">
    <!-- 队伍基础信息 -->
    <el-form-item label="队伍名称" prop="teamName">
      <el-input v-model="teamForm.teamName" />
    </el-form-item>
    
    <!-- 物资配置 -->
    <el-form-item label="物资配置">
      <el-button @click="addMaterial">添加物资</el-button>
      <div v-for="(material, index) in teamForm.materials" :key="index">
        <el-input v-model="material.materialName" placeholder="物资名称" />
        <el-input v-model="material.quantity" placeholder="数量" />
        <el-button @click="removeMaterial(index)">删除</el-button>
      </div>
    </el-form-item>
    
    <!-- 装备配置 -->
    <el-form-item label="装备配置">
      <el-button @click="addEquipment">添加装备</el-button>
      <div v-for="(equipment, index) in teamForm.equipments" :key="index">
        <el-input v-model="equipment.materialName" placeholder="装备名称" />
        <el-input v-model="equipment.quantity" placeholder="数量" />
        <el-button @click="removeEquipment(index)">删除</el-button>
      </div>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">创建队伍</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      teamForm: {
        teamName: '',
        teamCode: '',
        // ... 其他字段
        materials: [],
        equipments: []
      }
    }
  },
  methods: {
    addMaterial() {
      this.teamForm.materials.push({
        materialName: '',
        specModel: '',
        categoryCode: '',
        quantity: 0,
        unit: '',
        remark: ''
      });
    },
    
    addEquipment() {
      this.teamForm.equipments.push({
        materialName: '',
        specModel: '',
        categoryCode: '',
        quantity: 0,
        unit: '',
        remark: ''
      });
    },
    
    async submitForm() {
      try {
        const response = await this.$http.post('/rescue/team', this.teamForm);
        if (response.data.code === 200) {
          this.$message.success('救援队伍创建成功');
          this.$router.push('/rescue/team/list');
        }
      } catch (error) {
        this.$message.error('创建失败');
      }
    }
  }
}
</script>
```

## 注意事项

1. **物资归属**：创建的物资和装备都归属于该救援队伍，`team_id`字段会被设置
2. **类型区分**：系统自动设置物资类型，materials数组创建应急物资，equipments数组创建应急装备
3. **数据完整性**：建议前端进行基础验证，确保必填字段不为空
4. **权限控制**：需要`rescue:team:add`权限才能调用此接口
5. **编号唯一性**：系统会自动检查队伍编号的唯一性

这种设计确保了每个救援队伍都有自己专属的物资和装备配置，符合实际业务需求！
