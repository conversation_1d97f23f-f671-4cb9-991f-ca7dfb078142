package com.tocc.service.impl;

import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.vo.RescueCircleVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.mapper.EmergencyEventMapper;
import com.tocc.service.IRescueCircleService;
import com.tocc.service.IWarehouseService;
import com.tocc.service.IRescueTeamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应急救援圈Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class RescueCircleServiceImpl implements IRescueCircleService {
    
    private static final Logger log = LoggerFactory.getLogger(RescueCircleServiceImpl.class);
    
    @Autowired
    private EmergencyEventMapper emergencyEventMapper;
    
    @Autowired
    private IWarehouseService warehouseService;
    
    @Autowired
    private IRescueTeamService rescueTeamService;
    
    /**
     * 根据应急事件ID获取救援圈信息
     */
    @Override
    public RescueCircleVO getRescueCircleByEventId(String eventId) {
        // 查询应急事件信息
        EmergencyEventDTO event = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (event == null) {
            log.warn("未找到应急事件，事件ID: {}", eventId);
            return null;
        }
        
        if (event.getLongitude() == null || event.getLatitude() == null) {
            log.warn("应急事件缺少坐标信息，事件ID: {}", eventId);
            return null;
        }
        
        // 构建救援圈信息
        RescueCircleVO rescueCircle = getRescueCircleByCoordinates(event.getLongitude(), event.getLatitude());
        rescueCircle.setEventId(eventId);
        rescueCircle.setEventTitle(event.getEventTitle());
        
        return rescueCircle;
    }
    
    /**
     * 根据坐标获取救援圈信息
     */
    @Override
    public RescueCircleVO getRescueCircleByCoordinates(BigDecimal longitude, BigDecimal latitude) {
        RescueCircleVO rescueCircle = new RescueCircleVO();
        rescueCircle.setLongitude(longitude.toString());
        rescueCircle.setLatitude(latitude.toString());
        
        // 查询20km和40km范围内的仓库
        List<WarehouseVO> warehouses20km = getWarehousesWithinRadius(longitude, latitude, 20.0);
        List<WarehouseVO> warehouses40km = getWarehousesWithinRadius(longitude, latitude, 40.0);
        
        // 查询20km和40km范围内的救援队伍
        List<RescueTeamVO> rescueTeams20km = getRescueTeamsWithinRadius(longitude, latitude, 20.0);
        List<RescueTeamVO> rescueTeams40km = getRescueTeamsWithinRadius(longitude, latitude, 40.0);
        
        // 设置数据
        rescueCircle.setWarehouses20km(warehouses20km);
        rescueCircle.setWarehouses40km(warehouses40km);
        rescueCircle.setRescueTeams20km(rescueTeams20km);
        rescueCircle.setRescueTeams40km(rescueTeams40km);
        
        // 设置统计数量
        rescueCircle.setWarehouseCount20km(warehouses20km.size());
        rescueCircle.setWarehouseCount40km(warehouses40km.size());
        rescueCircle.setRescueTeamCount20km(rescueTeams20km.size());
        rescueCircle.setRescueTeamCount40km(rescueTeams40km.size());
        
        return rescueCircle;
    }
    
    /**
     * 查询指定范围内的仓库
     */
    @Override
    public List<WarehouseVO> getWarehousesWithinRadius(BigDecimal longitude, BigDecimal latitude, double radiusKm) {
        // 查询所有仓库
        List<WarehouseVO> allWarehouses = warehouseService.selectWarehouseList(new WarehouseDTO());
        
        // 过滤出指定范围内的仓库并设置距离
        return allWarehouses.stream()
                .filter(warehouse -> {
                    if (warehouse.getLongitude() == null || warehouse.getLatitude() == null) {
                        return false;
                    }
                    try {
                        double warehouseLon = Double.parseDouble(warehouse.getLongitude());
                        double warehouseLat = Double.parseDouble(warehouse.getLatitude());
                        double distance = calculateDistance(longitude.doubleValue(), latitude.doubleValue(),
                                                          warehouseLon, warehouseLat);
                        warehouse.setDistance(distance); // 设置距离
                        return distance <= radiusKm;
                    } catch (NumberFormatException e) {
                        log.warn("仓库坐标格式错误，仓库ID: {}", warehouse.getId());
                        return false;
                    }
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 查询指定范围内的救援队伍
     */
    @Override
    public List<RescueTeamVO> getRescueTeamsWithinRadius(BigDecimal longitude, BigDecimal latitude, double radiusKm) {
        // 查询所有救援队伍
        List<RescueTeamVO> allTeams = rescueTeamService.selectRescueTeamList(new RescueTeamDTO());
        
        // 过滤出指定范围内的救援队伍并设置距离
        return allTeams.stream()
                .filter(team -> {
                    if (team.getLongitude() == null || team.getLatitude() == null) {
                        return false;
                    }
                    try {
                        double teamLon = team.getLongitude().doubleValue();
                        double teamLat = team.getLatitude().doubleValue();
                        double distance = calculateDistance(longitude.doubleValue(), latitude.doubleValue(),
                                                          teamLon, teamLat);
                        team.setDistance(distance); // 设置距离
                        return distance <= radiusKm;
                    } catch (Exception e) {
                        log.warn("救援队伍坐标格式错误，队伍ID: {}", team.getId());
                        return false;
                    }
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 计算两点之间的距离（公里）
     * 使用Haversine公式计算球面距离
     */
    @Override
    public double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        final double EARTH_RADIUS = 6371.0; // 地球半径，单位：公里
        
        // 将角度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);
        
        // Haversine公式
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return EARTH_RADIUS * c;
    }
}
