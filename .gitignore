*.class

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.ear

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

######################
# 解决maven产生的文件
######################

target/
**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

######################
# 解决各类编辑器自动产生的文件
######################

*.iml

## Directory-based project format:
.idea/

## File-based project format:
*.ipr
*.iws

## Plugin-specific files:

# IntelliJ
/out/
/target/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties

/.settings/
/target/

# built application files
*.apk
*.ap_

# files for the dex VM
*.dex


# generated files
bin/
gen/

# Eclipse project files
.classpath
.project
.DS_Store
.factorypath
*.prefs

.sts4-cache/
LOG_PATH_IS_UNDEFINED/
transaction-logs/

# 前端
build/

# 日志
*.log
