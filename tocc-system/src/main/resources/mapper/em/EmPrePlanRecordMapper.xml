<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmPrePlanRecordMapper">

    <resultMap type="EmPrePlanRecord" id="EmPrePlanRecordResult">
        <result property="id"    column="id"    />
        <result property="prePlanId"    column="pre_plan_id"    />
        <result property="version"    column="version"    />
        <result property="planName"    column="plan_name"    />
        <result property="planType"    column="plan_type"    />
        <result property="applicableDeptIds"    column="applicable_dept_ids"    />
        <result property="compilingDept"    column="compiling_dept"    />
        <result property="purpose"    column="purpose"    />
        <result property="basis"    column="basis"    />
        <result property="scope"    column="scope"    />
        <result property="workPrinciple"    column="work_principle"    />
        <result property="preventiveMeasures"    column="preventive_measures"    />
        <result property="warningPrinciple"    column="warning_principle"    />
        <result property="warningInfoCollect"    column="warning_info_collect"    />
        <result property="warningLevel"    column="warning_level"    />
        <result property="warningPublish"    column="warning_publish"    />
        <result property="warningMeasures"    column="warning_measures"    />
        <result property="eventLevel"    column="event_level"    />
        <result property="responseCondition"    column="response_condition"    />
        <result property="processFlow"    column="process_flow"    />
        <result property="infoReport"    column="info_report"    />
        <result property="newsRelease"    column="news_release"    />
        <result property="responseAdjust"    column="response_adjust"    />
        <result property="aftermathDisposal"    column="aftermath_disposal"    />
        <result property="summaryEvaluation"    column="summary_evaluation"    />
        <result property="materialSupport"    column="material_support"    />
        <result property="communicationSupport"    column="communication_support"    />
        <result property="trafficSupport"    column="traffic_support"    />
        <result property="healthGuarantee"    column="health_guarantee"    />
        <result property="fundingSupport"    column="funding_support"    />
        <result property="planRevision"    column="plan_revision"    />
        <result property="publicityTraining"    column="publicity_training"    />
        <result property="planDrill"    column="plan_drill"    />
        <result property="implementTime"    column="implement_time"    />
        <result property="planStatus"    column="plan_status"    />
        <result property="enableStatus"    column="enable_status"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="lastCheckTime"    column="last_check_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="reviser"    column="reviser"    />
        <result property="revisionTime"    column="revision_time"    />
        <result property="revisionContent"    column="revision_content"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmPrePlanRecordVo">
        select id, pre_plan_id, version, plan_name, plan_type, compiling_dept,
               applicable_dept_ids, purpose, basis, scope,
               work_principle, preventive_measures, warning_principle, warning_info_collect, warning_level,
               warning_publish, warning_measures, event_level,
               response_condition, process_flow, info_report, news_release,
               response_adjust, aftermath_disposal, summary_evaluation,
               material_support, communication_support, traffic_support,
               health_guarantee, funding_support, plan_revision, publicity_training, plan_drill,
               implement_time, plan_status, enable_status, check_status,
               last_check_time, create_time, creator, update_time,
               updater, reviser, revision_time, revision_content, del_flag from em_pre_plan_record
    </sql>

    <select id="selectEmPrePlanRecordList" parameterType="EmPrePlanRecord" resultMap="EmPrePlanRecordResult">
        <include refid="selectEmPrePlanRecordVo"/>
        <where>
            del_flag = 0
            <if test="prePlanId != null  and prePlanId != ''"> and pre_plan_id = #{prePlanId}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="compilingUnit != null  and compilingUnit != ''"> and compiling_unit = #{compilingUnit}</if>
            <if test="applicableUnit != null  and applicableUnit != ''"> and applicable_unit = #{applicableUnit}</if>
            <if test="purpose != null  and purpose != ''"> and purpose = #{purpose}</if>
            <if test="basis != null  and basis != ''"> and basis = #{basis}</if>
            <if test="scope != null  and scope != ''"> and scope = #{scope}</if>
            <if test="workPrinciple != null  and workPrinciple != ''"> and work_principle = #{workPrinciple}</if>
            <if test="preventiveMeasures != null  and preventiveMeasures != ''"> and preventive_measures = #{preventiveMeasures}</if>
            <if test="warningPrinciple != null  and warningPrinciple != ''"> and warning_principle = #{warningPrinciple}</if>
            <if test="warningInfoCollect != null  and warningInfoCollect != ''"> and warning_info_collect = #{warningInfoCollect}</if>
            <if test="warningLevel != null  and warningLevel != ''"> and warning_level = #{warningLevel}</if>
            <if test="warningPublish != null  and warningPublish != ''"> and warning_publish = #{warningPublish}</if>
            <if test="warningMeasures != null  and warningMeasures != ''"> and warning_measures = #{warningMeasures}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="responseCondition != null  and responseCondition != ''"> and response_condition = #{responseCondition}</if>
            <if test="processFlow != null  and processFlow != ''"> and process_flow = #{processFlow}</if>
            <if test="infoReport != null  and infoReport != ''"> and info_report = #{infoReport}</if>
            <if test="newsRelease != null  and newsRelease != ''"> and news_release = #{newsRelease}</if>
            <if test="responseAdjust != null  and responseAdjust != ''"> and response_adjust = #{responseAdjust}</if>
            <if test="aftermathDisposal != null  and aftermathDisposal != ''"> and aftermath_disposal = #{aftermathDisposal}</if>
            <if test="summaryEvaluation != null  and summaryEvaluation != ''"> and summary_evaluation = #{summaryEvaluation}</if>
            <if test="materialSupport != null  and materialSupport != ''"> and material_support = #{materialSupport}</if>
            <if test="communicationSupport != null  and communicationSupport != ''"> and communication_support = #{communicationSupport}</if>
            <if test="trafficSupport != null  and trafficSupport != ''"> and traffic_support = #{trafficSupport}</if>
            <if test="healthGuarantee != null  and healthGuarantee != ''"> and health_guarantee = #{healthGuarantee}</if>
            <if test="fundingSupport != null  and fundingSupport != ''"> and funding_support = #{fundingSupport}</if>
            <if test="planRevision != null  and planRevision != ''"> and plan_revision = #{planRevision}</if>
            <if test="publicityTraining != null  and publicityTraining != ''"> and publicity_training = #{publicityTraining}</if>
            <if test="planDrill != null  and planDrill != ''"> and plan_drill = #{planDrill}</if>
            <if test="implementTime != null  and implementTime != ''"> and implement_time = #{implementTime}</if>
            <if test="planStatus != null "> and plan_status = #{planStatus}</if>
            <if test="enableStatus != null "> and enable_status = #{enableStatus}</if>
            <if test="checkStatus != null "> and check_status = #{checkStatus}</if>
            <if test="lastCheckTime != null "> and last_check_time = #{lastCheckTime}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="reviser != null  and reviser != ''"> and reviser = #{reviser}</if>
            <if test="revisionTime != null "> and revision_time = #{revisionTime}</if>
            <if test="revisionContent != null  and revisionContent != ''"> and revision_content = #{revisionContent}</if>
        </where>
    </select>

    <select id="selectEmPrePlanRecordById" parameterType="String" resultMap="EmPrePlanRecordResult">
        <include refid="selectEmPrePlanRecordVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmPrePlanRecord" parameterType="EmPrePlanRecord">
        insert into em_pre_plan_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="prePlanId != null">pre_plan_id,</if>
            <if test="version != null">version,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planType != null">plan_type,</if>
            <if test="compilingDept != null">compiling_dept,</if>
            <if test="applicableDeptIds != null">applicable_dept_ids,</if>
            <if test="purpose != null">purpose,</if>
            <if test="basis != null">basis,</if>
            <if test="scope != null">scope,</if>
            <if test="workPrinciple != null">work_principle,</if>
            <if test="preventiveMeasures != null">preventive_measures,</if>
            <if test="warningPrinciple != null">warning_principle,</if>
            <if test="warningInfoCollect != null">warning_info_collect,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="warningPublish != null">warning_publish,</if>
            <if test="warningMeasures != null">warning_measures,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="responseCondition != null">response_condition,</if>
            <if test="processFlow != null">process_flow,</if>
            <if test="infoReport != null">info_report,</if>
            <if test="newsRelease != null">news_release,</if>
            <if test="responseAdjust != null">response_adjust,</if>
            <if test="aftermathDisposal != null">aftermath_disposal,</if>
            <if test="summaryEvaluation != null">summary_evaluation,</if>
            <if test="materialSupport != null">material_support,</if>
            <if test="communicationSupport != null">communication_support,</if>
            <if test="trafficSupport != null">traffic_support,</if>
            <if test="healthGuarantee != null">health_guarantee,</if>
            <if test="fundingSupport != null">funding_support,</if>
            <if test="planRevision != null">plan_revision,</if>
            <if test="publicityTraining != null">publicity_training,</if>
            <if test="planDrill != null">plan_drill,</if>
            <if test="implementTime != null">implement_time,</if>
            <if test="planStatus != null">plan_status,</if>
            <if test="enableStatus != null">enable_status,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="lastCheckTime != null">last_check_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="reviser != null">reviser,</if>
            <if test="revisionTime != null">revision_time,</if>
            <if test="revisionContent != null">revision_content,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="prePlanId != null">#{prePlanId},</if>
            <if test="version != null">#{version},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planType != null">#{planType},</if>
            <if test="compilingDept != null">#{compilingDept},</if>
            <if test="applicableDeptIds != null">#{applicableDeptIds},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="basis != null">#{basis},</if>
            <if test="scope != null">#{scope},</if>
            <if test="workPrinciple != null">#{workPrinciple},</if>
            <if test="preventiveMeasures != null">#{preventiveMeasures},</if>
            <if test="warningPrinciple != null">#{warningPrinciple},</if>
            <if test="warningInfoCollect != null">#{warningInfoCollect},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="warningPublish != null">#{warningPublish},</if>
            <if test="warningMeasures != null">#{warningMeasures},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="responseCondition != null">#{responseCondition},</if>
            <if test="processFlow != null">#{processFlow},</if>
            <if test="infoReport != null">#{infoReport},</if>
            <if test="newsRelease != null">#{newsRelease},</if>
            <if test="responseAdjust != null">#{responseAdjust},</if>
            <if test="aftermathDisposal != null">#{aftermathDisposal},</if>
            <if test="summaryEvaluation != null">#{summaryEvaluation},</if>
            <if test="materialSupport != null">#{materialSupport},</if>
            <if test="communicationSupport != null">#{communicationSupport},</if>
            <if test="trafficSupport != null">#{trafficSupport},</if>
            <if test="healthGuarantee != null">#{healthGuarantee},</if>
            <if test="fundingSupport != null">#{fundingSupport},</if>
            <if test="planRevision != null">#{planRevision},</if>
            <if test="publicityTraining != null">#{publicityTraining},</if>
            <if test="planDrill != null">#{planDrill},</if>
            <if test="implementTime != null">#{implementTime},</if>
            <if test="planStatus != null">#{planStatus},</if>
            <if test="enableStatus != null">#{enableStatus},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="lastCheckTime != null">#{lastCheckTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="reviser != null">#{reviser},</if>
            <if test="revisionTime != null">#{revisionTime},</if>
            <if test="revisionContent != null">#{revisionContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmPrePlanRecord" parameterType="EmPrePlanRecord">
        update em_pre_plan_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="prePlanId != null">pre_plan_id = #{prePlanId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="compilingDept != null">compiling_dept = #{compilingDept},</if>
            <if test="applicableDeptIds != null">applicable_dept_ids = #{applicableDeptIds},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="basis != null">basis = #{basis},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="workPrinciple != null">work_principle = #{workPrinciple},</if>
            <if test="preventiveMeasures != null">preventive_measures = #{preventiveMeasures},</if>
            <if test="warningPrinciple != null">warning_principle = #{warningPrinciple},</if>
            <if test="warningInfoCollect != null">warning_info_collect = #{warningInfoCollect},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningPublish != null">warning_publish = #{warningPublish},</if>
            <if test="warningMeasures != null">warning_measures = #{warningMeasures},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="responseCondition != null">response_condition = #{responseCondition},</if>
            <if test="processFlow != null">process_flow = #{processFlow},</if>
            <if test="infoReport != null">info_report = #{infoReport},</if>
            <if test="newsRelease != null">news_release = #{newsRelease},</if>
            <if test="responseAdjust != null">response_adjust = #{responseAdjust},</if>
            <if test="aftermathDisposal != null">aftermath_disposal = #{aftermathDisposal},</if>
            <if test="summaryEvaluation != null">summary_evaluation = #{summaryEvaluation},</if>
            <if test="materialSupport != null">material_support = #{materialSupport},</if>
            <if test="communicationSupport != null">communication_support = #{communicationSupport},</if>
            <if test="trafficSupport != null">traffic_support = #{trafficSupport},</if>
            <if test="healthGuarantee != null">health_guarantee = #{healthGuarantee},</if>
            <if test="fundingSupport != null">funding_support = #{fundingSupport},</if>
            <if test="planRevision != null">plan_revision = #{planRevision},</if>
            <if test="publicityTraining != null">publicity_training = #{publicityTraining},</if>
            <if test="planDrill != null">plan_drill = #{planDrill},</if>
            <if test="implementTime != null">implement_time = #{implementTime},</if>
            <if test="planStatus != null">plan_status = #{planStatus},</if>
            <if test="enableStatus != null">enable_status = #{enableStatus},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="lastCheckTime != null">last_check_time = #{lastCheckTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="reviser != null">reviser = #{reviser},</if>
            <if test="revisionTime != null">revision_time = #{revisionTime},</if>
            <if test="revisionContent != null">revision_content = #{revisionContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmPrePlanRecordById" parameterType="String">
        update  em_pre_plan_record set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmPrePlanRecordByIds" parameterType="String">
        delete from em_pre_plan_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
