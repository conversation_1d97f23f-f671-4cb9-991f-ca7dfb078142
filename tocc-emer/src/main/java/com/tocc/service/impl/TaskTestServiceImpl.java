package com.tocc.service.impl;

import com.tocc.common.utils.StringUtils;
import com.tocc.domain.vo.TaskExecutionResultVO;
import com.tocc.quartz.task.InfoUpdateTimeoutCheckTask;
import com.tocc.service.ITaskTestService;
import com.tocc.service.IAlarmService;
import com.tocc.service.IRescueTeamService;
import com.tocc.service.IWarehouseService;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.system.service.ISysDictDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 定时任务测试Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TaskTestServiceImpl implements ITaskTestService {
    
    private static final Logger log = LoggerFactory.getLogger(TaskTestServiceImpl.class);
    
    @Autowired
    private InfoUpdateTimeoutCheckTask infoUpdateTimeoutCheckTask;
    
    @Autowired
    private ISysDictDataService dictDataService;
    
    @Autowired
    private IEmPrePlanService prePlanService;
    
    @Autowired
    private IRescueTeamService rescueTeamService;
    
    @Autowired
    private IWarehouseService warehouseService;
    
    @Autowired
    private IAlarmService alarmService;
    
    /**
     * 执行信息更新超时检查任务并返回详细结果
     */
    @Override
    public TaskExecutionResultVO executeTimeoutCheckWithDetails(String params) {
        TaskExecutionResultVO result = new TaskExecutionResultVO();
        List<String> executionSteps = new ArrayList<>();
        Date startTime = new Date();
        result.setStartTime(startTime);
        
        try {
            log.info("开始执行信息更新超时检查任务测试，参数：{}", params);
            executionSteps.add("开始执行信息更新超时检查任务，参数：" + params);
            
            // 获取超时阈值配置
            Map<String, Integer> timeoutThresholds = getTimeoutThresholds();
            result.setTimeoutThresholds(timeoutThresholds);
            executionSteps.add("获取超时阈值配置：" + timeoutThresholds);
            
            // 统计执行前的告警数量
            int alarmCountBefore = getCurrentAlarmCount();
            executionSteps.add("执行前告警总数：" + alarmCountBefore);
            
            // 执行定时任务
            executionSteps.add("开始执行定时任务...");
            infoUpdateTimeoutCheckTask.execute(params);
            executionSteps.add("定时任务执行完成");
            
            // 统计执行后的告警数量
            int alarmCountAfter = getCurrentAlarmCount();
            int createdAlarmCount = alarmCountAfter - alarmCountBefore;
            result.setCreatedAlarmCount(createdAlarmCount);
            executionSteps.add("执行后告警总数：" + alarmCountAfter + "，新增告警：" + createdAlarmCount);
            
            // 统计各类数据
            collectStatistics(result, executionSteps);
            
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(true);
            result.setMessage("任务执行成功");
            result.setExecutionSteps(executionSteps);
            
            log.info("信息更新超时检查任务测试执行完成，耗时：{}ms", result.getDuration());
            
        } catch (Exception e) {
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(false);
            result.setMessage("任务执行失败");
            result.setErrorMessage(e.getMessage());
            result.setExecutionSteps(executionSteps);
            
            log.error("信息更新超时检查任务测试执行失败", e);
        }
        
        return result;
    }
    
    /**
     * 检查任务执行环境
     */
    @Override
    public TaskExecutionResultVO checkTaskEnvironment() {
        TaskExecutionResultVO result = new TaskExecutionResultVO();
        List<String> executionSteps = new ArrayList<>();
        Date startTime = new Date();
        result.setStartTime(startTime);
        
        try {
            log.info("开始检查任务执行环境");
            executionSteps.add("开始检查任务执行环境");
            
            // 检查字典配置
            Map<String, Integer> timeoutThresholds = getTimeoutThresholds();
            result.setTimeoutThresholds(timeoutThresholds);
            executionSteps.add("字典配置检查完成：" + timeoutThresholds);
            
            // 检查各服务是否可用
            checkServiceAvailability(executionSteps);
            
            // 统计数据量
            collectStatistics(result, executionSteps);
            
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(true);
            result.setMessage("环境检查完成");
            result.setExecutionSteps(executionSteps);
            
            log.info("任务执行环境检查完成");
            
        } catch (Exception e) {
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(false);
            result.setMessage("环境检查失败");
            result.setErrorMessage(e.getMessage());
            result.setExecutionSteps(executionSteps);
            
            log.error("任务执行环境检查失败", e);
        }
        
        return result;
    }
    
    /**
     * 获取超时阈值配置
     */
    private Map<String, Integer> getTimeoutThresholds() {
        Map<String, Integer> thresholds = new HashMap<>();
        
        try {
            String prePlanThreshold = dictDataService.selectDictLabel("info_update_timeout", "应急预案更新阈值");
            thresholds.put("应急预案更新阈值", StringUtils.isNotEmpty(prePlanThreshold) ? Integer.parseInt(prePlanThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("应急预案更新阈值", 1);
        }
        
        try {
            String rescueTeamThreshold = dictDataService.selectDictLabel("info_update_timeout", "救援队伍更新阈值");
            thresholds.put("救援队伍更新阈值", StringUtils.isNotEmpty(rescueTeamThreshold) ? Integer.parseInt(rescueTeamThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("救援队伍更新阈值", 1);
        }
        
        try {
            String warehouseThreshold = dictDataService.selectDictLabel("info_update_timeout", "物资仓库更新阈值");
            thresholds.put("物资仓库更新阈值", StringUtils.isNotEmpty(warehouseThreshold) ? Integer.parseInt(warehouseThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("物资仓库更新阈值", 1);
        }
        
        return thresholds;
    }
    
    /**
     * 获取当前告警数量
     */
    private int getCurrentAlarmCount() {
        try {
            // 这里需要根据实际的告警查询方法来实现
            // 暂时返回0，你可以根据实际情况修改
            return 0;
        } catch (Exception e) {
            log.warn("获取告警数量失败", e);
            return 0;
        }
    }
    
    /**
     * 检查服务可用性
     */
    private void checkServiceAvailability(List<String> executionSteps) {
        try {
            // 检查字典服务
            dictDataService.selectDictLabel("info_update_timeout", "应急预案更新阈值");
            executionSteps.add("字典服务检查：正常");
        } catch (Exception e) {
            executionSteps.add("字典服务检查：异常 - " + e.getMessage());
        }
        
        // 可以添加更多服务检查...
    }
    
    /**
     * 收集统计信息
     */
    private void collectStatistics(TaskExecutionResultVO result, List<String> executionSteps) {
        try {
            // 统计应急预案数量（这里需要根据实际方法调整）
            // result.setPrePlanCount(prePlanService.countAll());
            result.setPrePlanCount(0);
            executionSteps.add("应急预案总数：" + result.getPrePlanCount());
            
            // 统计救援队伍数量
            // result.setRescueTeamCount(rescueTeamService.countAll());
            result.setRescueTeamCount(0);
            executionSteps.add("救援队伍总数：" + result.getRescueTeamCount());
            
            // 统计物资仓库数量
            // result.setWarehouseCount(warehouseService.countAll());
            result.setWarehouseCount(0);
            executionSteps.add("物资仓库总数：" + result.getWarehouseCount());
            
        } catch (Exception e) {
            executionSteps.add("统计信息收集失败：" + e.getMessage());
        }
    }
}
