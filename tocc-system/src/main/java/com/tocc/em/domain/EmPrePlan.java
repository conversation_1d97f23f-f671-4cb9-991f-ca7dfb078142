package com.tocc.em.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急预案数据对象 em_pre_plan
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmPrePlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */

    private String id;

    /** 编制单位 */
    @Excel(name = "编制单位")
    private String compilingDept;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 预案名称 */
    @Excel(name = "预案名称")
    private String planName;

    /** 预案类型 */
    @Excel(name = "预案类型")
    private String planType;

    /** 适用单位 */
    @Excel(name = "适用单位")
    private String applicableDeptIds;

    /** 编制目的 */
    @Excel(name = "编制目的")
    private String purpose;

    /** 编制依据 */
    @Excel(name = "编制依据")
    private String basis;

    /** 适用范围 */
    @Excel(name = "适用范围")
    private String scope;



    /** 工作原则(富文本) */
    @Excel(name = "工作原则(富文本)")
    private String workPrinciple;

    /** 预防措施(富文本) */
    @Excel(name = "预防措施(富文本)")
    private String preventiveMeasures;

    /** 预警原则(富文本) */
    @Excel(name = "预警原则(富文本)")
    private String warningPrinciple;

    /** 预警信息收集(富文本) */
    @Excel(name = "预警信息收集(富文本)")
    private String warningInfoCollect;

    /** 预警分级(富文本) */
    @Excel(name = "预警分级(富文本)")
    private String warningLevel;

    /** 预警发布(富文本) */
    @Excel(name = "预警发布(富文本)")
    private String warningPublish;

    /** 预警措施(富文本) */
    @Excel(name = "预警措施(富文本)")
    private String warningMeasures;

    /** 事件级别 */
    @Excel(name = "事件级别")
    private String eventLevel;

    /** 响应启动条件 */
    @Excel(name = "响应启动条件")
    private String responseCondition;

    /** 应急处置流程(富文本) */
    @Excel(name = "应急处置流程(富文本)")
    private String processFlow;

    /** 信息报送(富文本) */
    @Excel(name = "信息报送(富文本)")
    private String infoReport;

    /** 新闻发布(富文本) */
    @Excel(name = "新闻发布(富文本)")
    private String newsRelease;

    /** 响应调整与终止(富文本) */
    @Excel(name = "响应调整与终止(富文本)")
    private String responseAdjust;

    /** 善后处置(富文本) */
    @Excel(name = "善后处置(富文本)")
    private String aftermathDisposal;

    /** 总结评估(富文本) */
    @Excel(name = "总结评估(富文本)")
    private String summaryEvaluation;

    /** 物资保障(富文本) */
    @Excel(name = "物资保障(富文本)")
    private String materialSupport;

    /** 通信保障(富文本) */
    @Excel(name = "通信保障(富文本)")
    private String communicationSupport;

    /** 交通保障(富文本) */
    @Excel(name = "交通保障(富文本)")
    private String trafficSupport;

    /** 医疗保障(富文本) */
    @Excel(name = "医疗保障(富文本)")
    private String healthGuarantee;

    /** 经费保障(富文本) */
    @Excel(name = "经费保障(富文本)")
    private String fundingSupport;

    /** 预案修订(富文本) */
    @Excel(name = "预案修订(富文本)")
    private String planRevision;

    /** 宣传培训(富文本) */
    @Excel(name = "宣传培训(富文本)")
    private String publicityTraining;

    /** 预案演练(富文本) */
    @Excel(name = "预案演练(富文本)")
    private String planDrill;

    /** 实施时间(富文本) */
    @Excel(name = "实施时间(富文本)")
    private String implementTime;

    /** 预案状态(0草稿/1提交) */
    @Excel(name = "预案状态(0草稿/1提交)")
    private Integer planStatus;

    /** 启用状态(0启用/1停止) */
    @Excel(name = "启用状态(0启用/1停止)")
    private Integer enableStatus;

    /** 检查状态(0未检查/1已检查) */
    @Excel(name = "检查状态(0未检查/1已检查)")
    private Integer checkStatus;

    /** 最新检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最新检查时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastCheckTime;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 修订人 */
    @Excel(name = "修订人")
    private String reviser;

    /** 修订时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修订时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date revisionTime;

    /** 修订内容 */
    @Excel(name = "修订内容")
    private String revisionContent;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;

}
