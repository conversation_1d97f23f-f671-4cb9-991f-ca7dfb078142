package com.tocc.service;

import com.tocc.domain.vo.TaskExecutionResultVO;

/**
 * 定时任务测试Service接口
 * 
 * <AUTHOR>
 */
public interface ITaskTestService {
    
    /**
     * 执行信息更新超时检查任务并返回详细结果
     * 
     * @param params 任务参数
     * @return 执行结果
     */
    TaskExecutionResultVO executeTimeoutCheckWithDetails(String params);
    
    /**
     * 检查任务执行环境
     * 
     * @return 环境检查结果
     */
    TaskExecutionResultVO checkTaskEnvironment();
}
