package com.tocc.service;

import com.tocc.domain.dto.AlarmInfoDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

/**
 * 告警服务测试类
 * 
 * <AUTHOR>
 */
public class AlarmServiceTest {

    @Test
    public void testCreateAlarmInfo() {
        // 创建测试告警数据
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmTitle("测试应急事件告警");
        alarmInfo.setAlarmType("2"); // 应急类型
        alarmInfo.setAlarmSubtype("emer_new"); // 新事件子类型
        alarmInfo.setAlarmLevel("3"); // 紧急级别
        alarmInfo.setAlarmContent("这是一个测试告警：发生新的应急事件");
        alarmInfo.setSourceId("test-event-001");
        alarmInfo.setSourceType("event");
        alarmInfo.setOrgId("test-org-001");
        alarmInfo.setOrgName("测试组织");
        alarmInfo.setStatus("0"); // 未处理
        alarmInfo.setAlarmTime(new Date());
        
        System.out.println("创建的告警信息：");
        System.out.println("告警标题：" + alarmInfo.getAlarmTitle());
        System.out.println("告警类型：" + alarmInfo.getAlarmType());
        System.out.println("告警级别：" + alarmInfo.getAlarmLevel());
        System.out.println("告警内容：" + alarmInfo.getAlarmContent());
        System.out.println("关联事件ID：" + alarmInfo.getSourceId());
        System.out.println("所属组织：" + alarmInfo.getOrgName());
        
        // 这里在实际环境中会调用 alarmService.insertAlarmInfo(alarmInfo)
        System.out.println("告警创建测试完成！");
    }

    @Test
    public void testEventLevelMapping() {
        System.out.println("事件级别到告警级别的映射测试：");
        
        // 测试事件级别映射
        String[] eventLevels = {"1", "2", "3", "4"};
        String[] expectedAlarmLevels = {"4", "3", "2", "1"};
        String[] eventLevelNames = {"Ⅰ级(特别重大)", "Ⅱ级(重大)", "Ⅲ级(较大)", "Ⅳ级(一般)"};
        String[] alarmLevelNames = {"严重", "紧急", "重要", "一般"};
        
        for (int i = 0; i < eventLevels.length; i++) {
            String eventLevel = eventLevels[i];
            String expectedAlarmLevel = expectedAlarmLevels[i];
            String actualAlarmLevel = mapEventLevelToAlarmLevel(eventLevel);
            
            System.out.println("事件级别 " + eventLevel + "(" + eventLevelNames[i] + ") -> " +
                             "告警级别 " + actualAlarmLevel + "(" + alarmLevelNames[i] + ")");
            
            assert expectedAlarmLevel.equals(actualAlarmLevel) : 
                "映射错误：期望 " + expectedAlarmLevel + "，实际 " + actualAlarmLevel;
        }
        
        System.out.println("事件级别映射测试通过！");
    }
    
    /**
     * 将事件级别映射为告警级别（复制自EmergencyEventServiceImpl）
     */
    private String mapEventLevelToAlarmLevel(String eventLevel) {
        if (eventLevel == null) {
            return "1"; // 默认一般级别
        }
        
        switch (eventLevel) {
            case "1": // 事件Ⅰ级(特别重大)
                return "4"; // 告警严重
            case "2": // 事件Ⅱ级(重大)
                return "3"; // 告警紧急
            case "3": // 事件Ⅲ级(较大)
                return "2"; // 告警重要
            case "4": // 事件Ⅳ级(一般)
                return "1"; // 告警一般
            default:
                return "1"; // 默认一般级别
        }
    }
}
