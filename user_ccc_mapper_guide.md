# UserCccMapper XML映射文件说明

## 文件位置
```
tocc-tel/src/main/resources/mapper/UserCccMapper.xml
```

## 映射内容

### 1. ResultMap映射
```xml
<resultMap type="com.tocc.common.domain.vo.UserCccVO" id="UserCccResult">
    <result property="userId"       column="user_id"       />
    <result property="loginName"    column="login_name"    />
    <result property="displayName"  column="display_name"  />
    <result property="accessKey"    column="access_key"    />
    <result property="accessSecret" column="access_secret" />
</resultMap>
```

### 2. 查询方法
```xml
<select id="selectByUserId" parameterType="String" resultMap="UserCccResult">
    select user_id, login_name, display_name, access_key, access_secret
    from user_ccc
    where user_id = #{userId}
</select>
```

## 对应的数据库表结构

实际的`user_ccc`表结构：

```sql
CREATE TABLE `user_ccc` (
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `login_name` varchar(50) NOT NULL COMMENT '坐席登录名，对应用户mobile',
  `display_name` varchar(20) NOT NULL COMMENT '坐席显示名，对应用户姓名',
  `access_key` varchar(50) DEFAULT NULL COMMENT 'AK',
  `access_secret` varchar(50) DEFAULT NULL COMMENT 'AS',
  UNIQUE KEY `user_id_unique` (`user_id`) USING BTREE COMMENT '用户ID唯一',
  UNIQUE KEY `login_name_unique` (`login_name`) USING BTREE COMMENT '登录名唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

## 使用方式

### 1. Mapper接口
```java
@Mapper
public interface UserCccMapper {
    UserCccVO selectByUserId(String userId);
}
```

### 2. 调用示例
```java
@Service
public class UserCccService {
    
    @Autowired
    private UserCccMapper userCccMapper;
    
    public UserCccVO getUserCccInfo(String userId) {
        return userCccMapper.selectByUserId(userId);
    }
}
```

### 3. 控制器使用
```java
@RestController
public class UserCccController {
    
    @Autowired
    private UserCccService userCccService;
    
    @GetMapping("/userCcc/{userId}")
    public AjaxResult getUserCcc(@PathVariable String userId) {
        UserCccVO userCcc = userCccService.getUserCccInfo(userId);
        return success(userCcc);
    }
}
```

## 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| user_id | varchar(36) | 用户ID，唯一键 |
| login_name | varchar(50) | 坐席登录名，对应用户mobile，唯一键 |
| display_name | varchar(20) | 坐席显示名，对应用户姓名 |
| access_key | varchar(50) | 阿里云访问密钥AK |
| access_secret | varchar(50) | 阿里云访问密钥AS |

## 注意事项

1. **数据库表**：确保数据库中存在`user_ccc`表，且字段名与映射文件中的column名称一致
2. **字段类型**：根据实际需要调整数据库字段的长度和类型
3. **索引优化**：建议在`user_id`字段上创建主键索引
4. **数据安全**：`access_key`和`access_secret`字段包含敏感信息，注意数据安全

## 扩展建议

如果需要添加更多功能，可以在Mapper接口和XML文件中添加：

### 1. 插入方法
```xml
<insert id="insertUserCcc" parameterType="com.tocc.common.domain.vo.UserCccVO">
    insert into user_ccc (user_id, login_name, display_name, access_key, access_secret)
    values (#{userId}, #{loginName}, #{displayName}, #{accessKey}, #{accessSecret})
</insert>
```

### 2. 更新方法
```xml
<update id="updateUserCcc" parameterType="com.tocc.common.domain.vo.UserCccVO">
    update user_ccc
    set login_name = #{loginName},
        display_name = #{displayName},
        access_key = #{accessKey},
        access_secret = #{accessSecret}
    where user_id = #{userId}
</update>
```

### 3. 删除方法
```xml
<delete id="deleteUserCcc" parameterType="String">
    delete from user_ccc where user_id = #{userId}
</delete>
```

这样就可以提供完整的CRUD操作支持了！
