package com.tocc.system.service.impl;

import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.mapper.ExpertInfoMapper;
import com.tocc.system.service.IExpertService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 专家信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ExpertServiceImpl implements IExpertService {
    
    private static final Logger log = LoggerFactory.getLogger(ExpertServiceImpl.class);
    
    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    
    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int insertExpertInfo(ExpertInfoDTO expertInfo) {
        return expertInfoMapper.insertExpertInfo(expertInfo);
    }
    
    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int updateExpertInfo(ExpertInfoDTO expertInfo) {
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }
    
    /**
     * 根据用户ID删除专家信息
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteExpertInfoByUserId(Long userId) {
        return expertInfoMapper.deleteExpertInfoByUserId(userId);
    }
    
    /**
     * 根据用户ID查询专家信息
     * 
     * @param userId 用户ID
     * @return 专家信息
     */
    @Override
    public ExpertInfoDTO selectExpertInfoByUserId(Long userId) {
        return expertInfoMapper.selectExpertInfoByUserId(userId);
    }
    
    /**
     * 查询超时的专家信息
     * 
     * @param timeoutTime 超时时间点
     * @return 超时的专家列表
     */
    @Override
    public List<ExpertInfoVO> selectTimeoutExperts(Date timeoutTime) {
        return expertInfoMapper.selectTimeoutExperts(timeoutTime);
    }
    
    /**
     * 批量删除专家信息
     * 
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    public int deleteExpertInfoByUserIds(Long[] userIds) {
        return expertInfoMapper.deleteExpertInfoByUserIds(userIds);
    }
    
    /**
     * 更新专家最后更新时间
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int updateLastUpdateTime(Long userId) {
        ExpertInfoDTO expertInfo = new ExpertInfoDTO();
        expertInfo.setUserId(userId);
        expertInfo.setUpdateTime(new Date());
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }
}
