package com.tocc.em.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tocc.common.annotation.Excel;
import com.tocc.em.dto.EmEventLevelDTO;
import com.tocc.em.dto.EmMeasureDTO;
import com.tocc.em.dto.EmPrePlanDeptDTO;
import com.tocc.em.dto.EmPrePlanFileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 应急预案数据对象VO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmPrePlanVO
{

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 编制单位 */
    @NotNull(message="编制单位不能为空")
    @Excel(name = "编制单位")
    @ApiModelProperty(value = "编制单位")
    private String compilingDept;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 预案名称 */
    @NotNull(message="预案名称不能为空")
    @Excel(name = "预案名称")
    @ApiModelProperty(value = "预案名称")
    private String planName;

    /** 预案类型 */
    @NotNull(message="预案类型不能为空")
    @Excel(name = "预案类型")
    @ApiModelProperty(value = "预案类型")
    private String planType;

    /** 适用单位 */
    @Excel(name = "适用单位")
    @ApiModelProperty(value = "适用单位")
    private String applicableDeptIds;

    /** 编制目的 */
    @Excel(name = "编制目的")
    @ApiModelProperty(value = "编制目的")
    private String purpose;

    /** 编制依据 */
    @Excel(name = "编制依据")
    @ApiModelProperty(value = "编制依据")
    private String basis;

    /** 适用范围 */
    @Excel(name = "适用范围")
    @ApiModelProperty(value = "适用范围")
    private String scope;


    /** 工作原则(富文本) */
    @Excel(name = "工作原则(富文本)")
    @ApiModelProperty(value = "工作原则")
    private String workPrinciple;

    /** 预防措施(富文本) */
    @Excel(name = "预防措施(富文本)")
    @ApiModelProperty(value = "预防措施")
    private String preventiveMeasures;

    /** 预警原则(富文本) */
    @Excel(name = "预警原则(富文本)")
    @ApiModelProperty(value = "预警原则")
    private String warningPrinciple;

    /** 预警信息收集(富文本) */
    @Excel(name = "预警信息收集(富文本)")
    @ApiModelProperty(value = "预警信息收集")
    private String warningInfoCollect;

    /** 预警分级(富文本) */
    @Excel(name = "预警分级(富文本)")
    @ApiModelProperty(value = "预警分级")
    private String warningLevel;

    /** 预警发布(富文本) */
    @Excel(name = "预警发布(富文本)")
    @ApiModelProperty(value = "预警发布")
    private String warningPublish;

    /** 预警措施(富文本) */
    @Excel(name = "预警措施(富文本)")
    @ApiModelProperty(value = "预警措施")
    private String warningMeasures;

    /** 事件级别 */
    @Excel(name = "事件级别")
    @ApiModelProperty(value = "事件级别")
    private String eventLevel;

    /** 响应启动条件 */
    @Excel(name = "响应启动条件")
    @ApiModelProperty(value = "响应启动条件")
    private String responseCondition;

    /** 应急处置流程(富文本) */
    @Excel(name = "应急处置流程(富文本)")
    @ApiModelProperty(value = "应急处置流程")
    private String processFlow;

    /** 信息报送(富文本) */
    @Excel(name = "信息报送(富文本)")
    @ApiModelProperty(value = "信息报送")
    private String infoReport;

    /** 新闻发布(富文本) */
    @Excel(name = "新闻发布(富文本)")
    @ApiModelProperty(value = "新闻发布")
    private String newsRelease;

    /** 响应调整与终止(富文本) */
    @Excel(name = "响应调整与终止(富文本)")
    @ApiModelProperty(value = "响应调整与终止")
    private String responseAdjust;

    /** 善后处置(富文本) */
    @Excel(name = "善后处置(富文本)")
    @ApiModelProperty(value = "善后处置")
    private String aftermathDisposal;

    /** 总结评估(富文本) */
    @Excel(name = "总结评估(富文本)")
    @ApiModelProperty(value = "总结评估")
    private String summaryEvaluation;

    /** 物资保障(富文本) */
    @Excel(name = "物资保障(富文本)")
    @ApiModelProperty(value = "物资保障")
    private String materialSupport;

    /** 通信保障(富文本) */
    @Excel(name = "通信保障(富文本)")
    @ApiModelProperty(value = "通信保障")
    private String communicationSupport;

    /** 交通保障(富文本) */
    @Excel(name = "交通保障(富文本)")
    @ApiModelProperty(value = "交通保障")
    private String trafficSupport;

    /** 医疗保障(富文本) */
    @Excel(name = "医疗保障(富文本)")
    @ApiModelProperty(value = "医疗保障(富文本)")
    private String healthGuarantee;

    /** 经费保障(富文本) */
    @Excel(name = "经费保障(富文本)")
    @ApiModelProperty(value = "经费保障")
    private String fundingSupport;

    /** 预案修订(富文本) */
    @Excel(name = "预案修订(富文本)")
    @ApiModelProperty(value = "预案修订")
    private String planRevision;

    /** 宣传培训(富文本) */
    @Excel(name = "宣传培训(富文本)")
    @ApiModelProperty(value = "宣传培训")
    private String publicityTraining;

    /** 预案演练(富文本) */
    @Excel(name = "预案演练(富文本)")
    @ApiModelProperty(value = "预案演练")
    private String planDrill;

    /** 实施时间(富文本) */
    @Excel(name = "实施时间(富文本)")
    @ApiModelProperty(value = "实施时间")
    private String implementTime;

    /** 预案状态(0草稿/1提交) */
    @Excel(name = "预案状态(0草稿/1提交)")
    @ApiModelProperty(value = "预案状态(0草稿/1提交)")
    private Integer planStatus;

    /** 启用状态(0启用/1停止) */
    @Excel(name = "启用状态(0启用/1停止)")
    @ApiModelProperty(value = "启用状态(0启用/1停止)")
    private Integer enableStatus;

    /** 检查状态(0未检查/1已检查) */
    @Excel(name = "检查状态(0未检查/1已检查)")
    @ApiModelProperty(value = "检查状态(0未检查/1已检查)")
    private Integer checkStatus;

    /** 最新检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最新检查时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新检查时间")
    private Date lastCheckTime;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 修订人 */
    @Excel(name = "修订人")
    @ApiModelProperty(value = "修订人")
    private String reviser;

    /** 修订时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修订时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "修订时间")
    private Date revisionTime;

    /** 修订内容 */
    @Excel(name = "修订内容")
    @ApiModelProperty(value = "修订内容")
    private String revisionContent;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;

    /**
     * 事件分级与响应
     */
    @ApiModelProperty(value = "事件分级与响应")
    @JsonProperty("levelDTOList")
    List<EmEventLevelDTO> levelDTOList;
    /**
     * 应急组织体系
     */
    @ApiModelProperty(value = "应急组织体系")
    @JsonProperty("emPrePlanDeptDTOList")
    List<EmPrePlanDeptDTO> emPrePlanDeptDTOList;

    /**
     * 处置措施
     */
    @ApiModelProperty(value = "处置措施")
    @JsonProperty("emMeasureDTOList")
    List<EmMeasureDTO> emMeasureDTOList;

    /**
     * 附件
     */
    @JsonProperty("emPrePlanFileDTOS")
    @ApiModelProperty(value = "附件")
    List<EmPrePlanFileDTO> emPrePlanFileDTOS;

}
