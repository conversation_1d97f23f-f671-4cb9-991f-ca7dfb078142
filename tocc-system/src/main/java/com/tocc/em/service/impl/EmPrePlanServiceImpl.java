package com.tocc.em.service.impl;

import java.beans.Transient;
import java.time.LocalDateTime;
import java.util.*;

import cn.hutool.core.date.DateUtil;
import com.tocc.common.core.domain.entity.SysDictData;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.bean.BeanUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.em.domain.*;
import com.tocc.em.dto.*;
import com.tocc.em.enums.EmPrePlanStatus;
import com.tocc.em.qo.EmEventLevelQO;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.service.*;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 应急预案数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanServiceImpl implements IEmPrePlanService
{
    @Autowired
    private EmPrePlanMapper emPrePlanMapper;

    @Resource
    private IEmPrePlanDeptService emPrePlanDeptService ;

    @Resource
    private IEmEventLevelService emEventLevelService ;

    @Resource
    private IEmMeasureService measureService ;

    @Resource
    private IEmPrePlanFileService fileService ;

    @Resource
    private IEmPrePlanRecordService prePlanRecordService ;

    /**
     * 查询应急预案数据
     *
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    @Override
    public EmPrePlanVO selectEmPrePlanById(String id)
    {   EmPrePlanVO emPrePlanVO = new EmPrePlanVO();
        EmPrePlan emPrePlan = emPrePlanMapper.selectEmPrePlanById(id);
        BeanUtils.copyProperties(emPrePlan,emPrePlanVO);
        String version = emPrePlan.getVersion();
        // 事件分级与响应
        EmEventLevelQO emEventLevelQO = new EmEventLevelQO();
        emEventLevelQO.setVersion(version);
        emEventLevelQO.setPrePlanId(id);
        List<EmEventLevel> emEventLevels = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        List<EmEventLevelDTO> levelDTOList = new ArrayList<>();
        for (EmEventLevel emEventLevel : emEventLevels) {
            EmEventLevelDTO emEventLevelDTO = new EmEventLevelDTO();
            //todo id为何没复制过去
            BeanUtils.copyProperties(emEventLevel,emEventLevelDTO);
            levelDTOList.add(emEventLevelDTO);
        }
        emPrePlanVO.setLevelDTOList( levelDTOList );
        // 应急组织体系
        EmPrePlanDept emPrePlanDept = new EmPrePlanDept();
        emPrePlanDept.setVersion(version);
        emPrePlanDept.setPrePlanId(id);
        emPrePlanVO.setEmPrePlanDeptDTOList(  emPrePlanDeptService.selectEmPrePlanDeptListWithTree(emPrePlanDept));
        //处置措施
        EmMeasure emMeasure = new EmMeasure();
        emMeasure.setVersion(version);
        emMeasure.setPrePlanId(id);
        List<EmMeasure> emMeasures = measureService.selectEmMeasureList(emMeasure);
        List<EmMeasureDTO> measureDTOList = new ArrayList<>();
        for (EmMeasure measure : emMeasures) {
            EmMeasureDTO emMeasureDTO = new EmMeasureDTO();
            BeanUtils.copyProperties(measure,emMeasureDTO);
            measureDTOList.add(emMeasureDTO);
        }
        emPrePlanVO.setEmMeasureDTOList(measureDTOList);
        // 附件
        EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
        emPrePlanFile.setVersion(version);
        emPrePlanFile.setBizId(id);
        List<EmPrePlanFile> emPrePlanFiles = fileService.selectEmPrePlanFileList(emPrePlanFile);
        List<EmPrePlanFileDTO> fileDTOList = new ArrayList<>();
        for (EmPrePlanFile prePlanFile : emPrePlanFiles) {
            EmPrePlanFileDTO emPrePlanFileDTO = new EmPrePlanFileDTO();
            BeanUtils.copyProperties(prePlanFile,emPrePlanFileDTO);
            fileDTOList.add(emPrePlanFileDTO);
        }
        emPrePlanVO.setEmPrePlanFileDTOS(fileDTOList);
        return emPrePlanVO;
    }

    /**
     * 查询应急预案数据列表
     *
     * @param emPrePlanQO 应急预案数据
     * @return 应急预案数据
     */
    @Override
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlanQO)
    {     EmPrePlan emPrePlan = new EmPrePlan();
        //1我的预案，2厅级预案，3市级预案，4直属单位预案，5草稿箱
//        if("1".equals(emPrePlanQO.getQueryType())){
//            String userName = SecurityUtils.getLoginUser().getUser().getUserName();
//            emPrePlanQO.setCreator(userName);
//        }
//        if("2".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("1");
//        }
//        if("3".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("3");
//        }
//        if("4".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("2");
//        }
//        if("5".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setPlanStatus(0);
//        }else {
//            emPrePlanQO.setPlanStatus(1);
//        }
//        //本周检状态 检查状态(0未检查/1已检查)
//        if(null != emPrePlanQO.getCheckStatus()){
//            Date weekStart = DateUtil.beginOfWeek(new Date());
//            Date weekEnd = DateUtil.endOfWeek(new Date());
//            Map<String ,Object> param = new HashMap<>();
//            param.put("weekStart",DateUtil.format(weekStart, "yyyy-MM-dd HH:mm:ss"));
//            param.put("weekEnd",DateUtil.format(weekEnd, "yyyy-MM-dd HH:mm:ss"));
//            emPrePlanQO.setParams(param);
//        }
        return emPrePlanMapper.selectEmPrePlanList(emPrePlanQO);
    }

    /**
     * 新增应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmPrePlan( EmPrePlanDTO emPrePlanDTO)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getUsername();
        EmPrePlan emPrePlan = new EmPrePlan();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlan);
        String emPrePlanID = IdUtils.fastSimpleUUID();
        emPrePlan.setId(emPrePlanID);
        emPrePlan.setVersion("0.1");
        emPrePlan.setCreator(username);
        emPrePlan.setUpdateBy(username);
        emPrePlanMapper.insertEmPrePlan(emPrePlan);

        // 事件分级与响应
        List<EmEventLevelDTO> levelDTOList =emPrePlanDTO.getLevelDTOList();
        List<EmEventLevel> emEventLevels = new ArrayList<>();
        for (EmEventLevelDTO emEventLevelDTO : levelDTOList) {
            EmEventLevel  eventLevel = new EmEventLevel();
            BeanUtils.copyProperties(emEventLevelDTO,eventLevel);
            eventLevel.setId(IdUtils.fastSimpleUUID());
            eventLevel.setPrePlanId(emPrePlanID);
            eventLevel.setCreator(username);
            eventLevel.setUpdater(username);
            eventLevel.setUpdateBy(username);
            eventLevel.setVersion(emPrePlan.getVersion());
            eventLevel.setVersion(emPrePlan.getVersion());
            emEventLevels.add(eventLevel);
        }
        emEventLevelService.insertBatchEmEventLevel(emEventLevels);
        // 应急组织体系
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList =emPrePlanDTO.getEmPrePlanDeptDTOList() ;
        if(null != emPrePlanDeptDTOList && !emPrePlanDeptDTOList.isEmpty()){
            emPrePlanDeptService.saveEmPrePlanDept(emPrePlan.getVersion(),emPrePlanID,emPrePlanDeptDTOList);
        }
        //处置措施
        List<EmMeasureDTO> emMeasureDTOList = emPrePlanDTO.getEmMeasureDTOList() ;
        if(null != emMeasureDTOList && !emMeasureDTOList.isEmpty()){
            for (EmMeasureDTO emMeasureDTO : emMeasureDTOList) {
                EmMeasure measure = new EmMeasure();
                BeanUtils.copyProperties(emMeasureDTO,measure);
                measure.setPrePlanId(emPrePlanID);
                measure.setCreator(username);
                measure.setUpdateBy(username);
                measure.setVersion(emPrePlan.getVersion());
                measure.setId(IdUtils.fastSimpleUUID());
                measureService.insertEmMeasure(measure);
            }
        }
        // 附件
        List<EmPrePlanFileDTO> emPrePlanFileDTOS = emPrePlanDTO.getEmPrePlanFileDTOS();
        if(null != emPrePlanFileDTOS && !emPrePlanFileDTOS.isEmpty()){
            for (EmPrePlanFileDTO emPrePlanFileDTO : emPrePlanFileDTOS) {
                EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
                BeanUtils.copyProperties(emPrePlanFileDTO,emPrePlanFile);
                emPrePlanFile.setBizId(emPrePlanID);
                emPrePlanFile.setCreator(username);
                emPrePlanFile.setUpdateBy(username);
                emPrePlanFile.setVersion(emPrePlan.getVersion());
                emPrePlanFile.setId(IdUtils.fastSimpleUUID());
                fileService.insertEmPrePlanFile(emPrePlanFile);
            }
        }

        return  1;
    }

    /**
     * 修改应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    @Override
    public int updateEmPrePlan(EmPrePlanDTO emPrePlanDTO)
    {
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();

        //查询更新前状态
        String prePlanId = emPrePlanDTO.getId();
        EmPrePlan emPrePlanOld = emPrePlanMapper.selectEmPrePlanById(prePlanId);
        Integer planStatusOld = emPrePlanOld.getPlanStatus();

        if(EmPrePlanStatus.SUBMITTED.getValue().equals(planStatusOld)){
            //新增版本并留痕
            addEmPrePlanVersion(emPrePlanDTO);
        }else{
            //直接更新
            EmPrePlan emPrePlan = new EmPrePlan();
            BeanUtils.copyProperties(emPrePlanDTO,emPrePlan);
            //清除 关联 start---
            clearAssociated(emPrePlanDTO, emPrePlan);
            //清除 关联 end---

            // 新增关联 start --
            addAssociated(emPrePlanDTO, userName);
            // 新增关联 end --
            emPrePlanMapper.updateEmPrePlan(emPrePlan);
        }
        return  1;
    }

    private void addAssociated(EmPrePlanDTO emPrePlanDTO, String userName) {
        String id =emPrePlanDTO.getId();
        // 事件分级与响应
        List<EmEventLevelDTO> levelDTOList = emPrePlanDTO.getLevelDTOList();
        List<EmEventLevel> emEventLevels = new ArrayList<>();
        for (EmEventLevelDTO emEventLevelDTO : levelDTOList) {
            EmEventLevel  eventLevel = new EmEventLevel();
            BeanUtils.copyProperties(emEventLevelDTO,eventLevel);
            eventLevel.setId(IdUtils.fastSimpleUUID());
            eventLevel.setPrePlanId(id);
            eventLevel.setCreator(userName);
            eventLevel.setUpdater(userName);
            eventLevel.setUpdateBy(userName);
            eventLevel.setVersion(emPrePlanDTO.getVersion());
            emEventLevels.add(eventLevel);
        }
        emEventLevelService.insertBatchEmEventLevel(emEventLevels);
        // 应急组织体系
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList = emPrePlanDTO.getEmPrePlanDeptDTOList() ;
        if(null != emPrePlanDeptDTOList && !emPrePlanDeptDTOList.isEmpty()){
            emPrePlanDeptService.saveEmPrePlanDept(emPrePlanDTO.getVersion(), id,emPrePlanDeptDTOList);
        }
        //处置措施
        List<EmMeasureDTO> emMeasureDTOList = emPrePlanDTO.getEmMeasureDTOList() ;
        if(null != emMeasureDTOList && !emMeasureDTOList.isEmpty()){
            for (EmMeasureDTO emMeasureDTO : emMeasureDTOList) {
                EmMeasure measure = new EmMeasure();
                BeanUtils.copyProperties(emMeasureDTO,measure);
                measure.setPrePlanId(id);
                measure.setCreator(userName);
                measure.setUpdateBy(userName);
                measure.setVersion(emPrePlanDTO.getVersion());
                measure.setId(IdUtils.fastSimpleUUID());
                measureService.insertEmMeasure(measure);
            }
        }
        // 附件
        List<EmPrePlanFileDTO> emPrePlanFileDTOS = emPrePlanDTO.getEmPrePlanFileDTOS();
        if(null != emPrePlanFileDTOS && !emPrePlanFileDTOS.isEmpty()){
            for (EmPrePlanFileDTO emPrePlanFileDTO : emPrePlanFileDTOS) {
                EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
                BeanUtils.copyProperties(emPrePlanFileDTO,emPrePlanFile);
                emPrePlanFile.setBizId(id);
                emPrePlanFile.setCreator(userName);
                emPrePlanFile.setUpdateBy(userName);
                emPrePlanFile.setVersion(emPrePlanDTO.getVersion());
                emPrePlanFile.setId(IdUtils.fastSimpleUUID());
                fileService.insertEmPrePlanFile(emPrePlanFile);
            }
        }
    }

    private String clearAssociated(EmPrePlanDTO emPrePlanDTO, EmPrePlan emPrePlan) {
        String version = emPrePlan.getVersion();
        String id = emPrePlanDTO.getId();
        // 事件分级与响应
        EmEventLevelQO emEventLevelQO = new EmEventLevelQO();
        emEventLevelQO.setVersion(version);
        emEventLevelQO.setPrePlanId(id);
        List<EmEventLevel> emEventLevels = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        for (EmEventLevel emEventLevel : emEventLevels) {
            emEventLevelService.deleteEmEventLevelById(emEventLevel.getId());
        }
        // 应急组织体系
        EmPrePlanDept emPrePlanDept = new EmPrePlanDept();
        emPrePlanDept.setVersion(version);
        emPrePlanDept.setPrePlanId(id);
        for (EmPrePlanDeptDTO emPrePlanDeptDTO : emPrePlanDeptService.selectEmPrePlanDeptListWithTree(emPrePlanDept)) {
            emPrePlanDeptService.deleteEmPrePlanDeptById(emPrePlanDeptDTO.getId());
        }
        //处置措施
        EmMeasure emMeasure = new EmMeasure();
        emMeasure.setVersion(version);
        emMeasure.setPrePlanId(id);
        List<EmMeasure> emMeasures = measureService.selectEmMeasureList(emMeasure);
        for (EmMeasure measure : emMeasures) {
            measureService.deleteEmMeasureById(measure.getId());
        }
        // 附件
        EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
        emPrePlanFile.setVersion(version);
        emPrePlanFile.setBizId(id);
        List<EmPrePlanFile> emPrePlanFiles = fileService.selectEmPrePlanFileList(emPrePlanFile);
        for (EmPrePlanFile prePlanFile : emPrePlanFiles) {
            fileService.deleteEmPrePlanFileById(prePlanFile.getId());
        }
        return id;
    }

    private void addEmPrePlanVersion(EmPrePlanDTO emPrePlanDTO) {
        //修订内容必填校验
        if (emPrePlanDTO.getRevisionContent() == null || emPrePlanDTO.getRevisionContent().isEmpty()) {
           throw  new RuntimeException("修订内容未录入！！");
        }
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        //留痕
        EmPrePlanRecord emPrePlanRecord = new EmPrePlanRecord();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlanRecord);
        emPrePlanRecord.setPrePlanId(emPrePlanDTO.getId());
        emPrePlanRecord.setId(IdUtils.fastSimpleUUID());
        prePlanRecordService.insertEmPrePlanRecord(emPrePlanRecord);


        //增加版本号
        String version = emPrePlanDTO.getVersion();
        String[] parts = version.split("\\.");
        int minor = Integer.parseInt(parts[1]) + 1;
        String  newVersion=  parts[0] + "." + minor;
        emPrePlanDTO.setVersion(newVersion);
        emPrePlanDTO.setReviser(userName);
        emPrePlanDTO.setRevisionTime(new Date());
        EmPrePlan emPrePlanNewVersion = new EmPrePlan();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlanNewVersion);
        emPrePlanMapper.updateEmPrePlan(emPrePlanNewVersion);

        addAssociated(emPrePlanDTO,userName);
    }

    /**
     * 批量删除应急预案数据
     *
     * @param ids 需要删除的应急预案数据主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanByIds(String[] ids)
    {
        return emPrePlanMapper.deleteEmPrePlanByIds(ids);
    }

    /**
     * 删除应急预案数据信息
     *
     * @param id 应急预案数据主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanById(String id)
    {
        return emPrePlanMapper.deleteEmPrePlanById(id);
    }

    /**
     * 查询更新超时的应急预案
     *
     * @param timeoutTime 超时时间点
     * @return 超时的应急预案集合
     */
    @Override
    public List<EmPrePlanVO> selectTimeoutPlans(LocalDateTime timeoutTime) {
        return emPrePlanMapper.selectTimeoutPlans(timeoutTime);
    }
}
