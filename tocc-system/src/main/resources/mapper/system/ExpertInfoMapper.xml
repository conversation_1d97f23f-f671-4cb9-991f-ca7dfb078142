<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.ExpertInfoMapper">
    
    <resultMap type="com.tocc.system.domain.dto.ExpertInfoDTO" id="ExpertInfoResult">
        <result property="userId"    column="user_id"    />
        <result property="specialtyField"    column="specialty_field"    />
        <result property="professionalTitle"    column="professional_title"    />
        <result property="workUnit"    column="work_unit"    />
        <result property="education"    column="education"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="address"    column="address"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="expertiseDescription"    column="expertise_description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.tocc.system.domain.vo.ExpertInfoVO" id="ExpertInfoVOResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="specialtyField"    column="specialty_field"    />
        <result property="professionalTitle"    column="professional_title"    />
        <result property="workUnit"    column="work_unit"    />
        <result property="education"    column="education"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="address"    column="address"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="expertiseDescription"    column="expertise_description"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="status"    column="status"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectExpertInfoVo">
        select user_id, specialty_field, professional_title, work_unit, education, phone, email, address, longitude, latitude, expertise_description, status, create_by, create_time, update_by, update_time, remark from expert_info
    </sql>

    <select id="selectExpertInfoByUserId" parameterType="Long" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectTimeoutExperts" parameterType="Date" resultMap="ExpertInfoVOResult">
        SELECT e.user_id, e.specialty_field, e.professional_title, e.work_unit, 
               e.phone, e.email, e.update_time, e.create_by,
               u.user_name, u.nick_name, d.dept_id, d.dept_name
        FROM expert_info e
        LEFT JOIN sys_user u ON e.user_id = u.user_id
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE e.status = '0'
          AND u.status = '0'
          AND e.update_time &lt; #{timeoutTime}
        ORDER BY e.update_time ASC
    </select>

    <insert id="insertExpertInfo" parameterType="com.tocc.system.domain.dto.ExpertInfoDTO">
        INSERT INTO expert_info (
            user_id, specialty_field, professional_title, work_unit, education,
            phone, email, address, longitude, latitude, expertise_description,
            status, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{userId}, #{specialtyField}, #{professionalTitle}, #{workUnit}, #{education},
            #{phone}, #{email}, #{address}, #{longitude}, #{latitude}, #{expertiseDescription},
            #{status}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <update id="updateExpertInfo" parameterType="com.tocc.system.domain.dto.ExpertInfoDTO">
        UPDATE expert_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="specialtyField != null">specialty_field = #{specialtyField},</if>
            <if test="professionalTitle != null">professional_title = #{professionalTitle},</if>
            <if test="workUnit != null">work_unit = #{workUnit},</if>
            <if test="education != null">education = #{education},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="expertiseDescription != null">expertise_description = #{expertiseDescription},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE user_id = #{userId}
    </update>

    <delete id="deleteExpertInfoByUserId" parameterType="Long">
        DELETE FROM expert_info WHERE user_id = #{userId}
    </delete>

    <delete id="deleteExpertInfoByUserIds" parameterType="Long">
        DELETE FROM expert_info WHERE user_id IN
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
