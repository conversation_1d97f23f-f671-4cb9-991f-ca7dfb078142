package com.tocc.web.emerController;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.service.IRescueTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 救援队伍Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "救援队伍管理")
@RestController
@RequestMapping("/rescue/team")
public class RescueTeamController extends BaseController {
    
    @Autowired
    private IRescueTeamService rescueTeamService;

    /**
     * 查询救援队伍列表
     */
    @ApiOperation("查询救援队伍列表")
    @PreAuthorize("@ss.hasPermi('rescue:team:list')")
    @GetMapping("/list")
    public TableDataInfo list(RescueTeamDTO rescueTeam) {
        startPage();
        List<RescueTeamVO> list = rescueTeamService.selectRescueTeamList(rescueTeam);
        return getDataTable(list);
    }

    /**
     * 导出救援队伍列表
     */
    @ApiOperation("导出救援队伍列表")
    @PreAuthorize("@ss.hasPermi('rescue:team:export')")
    @Log(title = "救援队伍", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RescueTeamDTO rescueTeam) {
        List<RescueTeamVO> list = rescueTeamService.selectRescueTeamList(rescueTeam);
        ExcelUtil<RescueTeamVO> util = new ExcelUtil<RescueTeamVO>(RescueTeamVO.class);
        util.exportExcel(response, list, "救援队伍数据");
    }

    /**
     * 获取救援队伍详细信息
     */
    @ApiOperation("获取救援队伍详细信息")
    @PreAuthorize("@ss.hasPermi('rescue:team:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(rescueTeamService.selectRescueTeamById(id));
    }

    /**
     * 新增救援队伍
     */
    @ApiOperation("新增救援队伍")
    @PreAuthorize("@ss.hasPermi('rescue:team:add')")
    @Log(title = "救援队伍", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RescueTeamDTO rescueTeam) {
        // 检查队伍编号唯一性
        if (rescueTeam.getTeamCode() != null && !rescueTeam.getTeamCode().isEmpty()) {
            if (!rescueTeamService.checkTeamCodeUnique(rescueTeam.getTeamCode(), null)) {
                return error("新增救援队伍'" + rescueTeam.getTeamName() + "'失败，队伍编号已存在");
            }
        }
        
        return toAjax(rescueTeamService.insertRescueTeam(rescueTeam));
    }

    /**
     * 修改救援队伍
     */
    @ApiOperation("修改救援队伍")
    @PreAuthorize("@ss.hasPermi('rescue:team:edit')")
    @Log(title = "救援队伍", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RescueTeamDTO rescueTeam) {
        // 检查队伍编号唯一性
        if (rescueTeam.getTeamCode() != null && !rescueTeam.getTeamCode().isEmpty()) {
            if (!rescueTeamService.checkTeamCodeUnique(rescueTeam.getTeamCode(), rescueTeam.getId())) {
                return error("修改救援队伍'" + rescueTeam.getTeamName() + "'失败，队伍编号已存在");
            }
        }
        
        return toAjax(rescueTeamService.updateRescueTeam(rescueTeam));
    }

    /**
     * 删除救援队伍
     */
    @ApiOperation("删除救援队伍")
    @PreAuthorize("@ss.hasPermi('rescue:team:remove')")
    @Log(title = "救援队伍", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(rescueTeamService.deleteRescueTeamByIds(ids));
    }

    /**
     * 检查队伍编号是否唯一
     */
    @ApiOperation("检查队伍编号是否唯一")
    @GetMapping("/checkTeamCodeUnique")
    public AjaxResult checkTeamCodeUnique(@RequestParam String teamCode, @RequestParam(required = false) String id) {
        boolean isUnique = rescueTeamService.checkTeamCodeUnique(teamCode, id);
        return success(isUnique);
    }

    /**
     * 查询救援队伍的物资列表
     */
    @ApiOperation("查询救援队伍的物资列表")
    @PreAuthorize("@ss.hasPermi('rescue:team:query')")
    @GetMapping("/{teamId}/materials")
    public AjaxResult getTeamMaterials(@PathVariable String teamId) {
        List<MaterialVO> materials = rescueTeamService.selectTeamMaterials(teamId);
        return success(materials);
    }

    /**
     * 查询救援队伍的装备列表
     */
    @ApiOperation("查询救援队伍的装备列表")
    @PreAuthorize("@ss.hasPermi('rescue:team:query')")
    @GetMapping("/{teamId}/equipments")
    public AjaxResult getTeamEquipments(@PathVariable String teamId) {
        List<MaterialVO> equipments = rescueTeamService.selectTeamEquipments(teamId);
        return success(equipments);
    }

    /**
     * 更新救援队伍的物资配置
     */
    @ApiOperation("更新救援队伍的物资配置")
    @PreAuthorize("@ss.hasPermi('rescue:team:edit')")
    @Log(title = "救援队伍物资配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{teamId}/materials")
    public AjaxResult updateTeamMaterials(@PathVariable String teamId, @RequestBody List<String> materialIds) {
        return toAjax(rescueTeamService.updateTeamMaterials(teamId, materialIds));
    }

    /**
     * 添加救援队伍物资
     */
    @ApiOperation("添加救援队伍物资")
    @PreAuthorize("@ss.hasPermi('rescue:team:edit')")
    @Log(title = "救援队伍物资", businessType = BusinessType.INSERT)
    @PostMapping("/{teamId}/material/{materialId}")
    public AjaxResult addTeamMaterial(@PathVariable String teamId, @PathVariable String materialId) {
        return toAjax(rescueTeamService.addTeamMaterial(teamId, materialId));
    }

    /**
     * 移除救援队伍物资
     */
    @ApiOperation("移除救援队伍物资")
    @PreAuthorize("@ss.hasPermi('rescue:team:edit')")
    @Log(title = "救援队伍物资", businessType = BusinessType.DELETE)
    @DeleteMapping("/{teamId}/material/{materialId}")
    public AjaxResult removeTeamMaterial(@PathVariable String teamId, @PathVariable String materialId) {
        return toAjax(rescueTeamService.removeTeamMaterial(teamId, materialId));
    }
}
