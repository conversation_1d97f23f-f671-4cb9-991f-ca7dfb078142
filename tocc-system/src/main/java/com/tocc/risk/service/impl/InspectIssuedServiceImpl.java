package com.tocc.risk.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.service.IInspectTaskService;
import com.tocc.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.InspectIssuedMapper;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.service.IInspectIssuedService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检查下发Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class InspectIssuedServiceImpl implements IInspectIssuedService 
{
    @Autowired
    private InspectIssuedMapper inspectIssuedMapper;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private IInspectTaskService inspectTaskService;


    /**
     * 查询检查下发
     * 
     * @param id 检查下发主键
     * @return 检查下发
     */
    @Override
    public InspectIssued selectInspectIssuedById(Long id)
    {
        return inspectIssuedMapper.selectInspectIssuedById(id);
    }

    /**
     * 查询检查下发列表
     * 
     * @param inspectIssued 检查下发
     * @return 检查下发
     */
    @Override
    public List<InspectIssued> selectInspectIssuedList(InspectIssued inspectIssued)
    {
        return inspectIssuedMapper.selectInspectIssuedList(inspectIssued);
    }

    @Override
    public List<InspectTask> getProgress(InspectIssued inspectIssued) {
        return inspectIssuedMapper.getProgress(inspectIssued.getId());
    }

    /**
     * 新增检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    @Override
    @Transactional
    public int insertInspectIssued(InspectIssued inspectIssued)
    {
        // 切割填报人ID，获取下发单位，
        String[] fillIds = inspectIssued.getFillId().split(",");
        String deptName = "";
        String ids = "";
        for (int i = 0; i < fillIds.length; i++) {
            String deptIds = deptMapper.getAncestorsBuUserId(fillIds[i]);
            String[] deptId = deptIds.split(",");
            SysDept dept = null;
            if (deptId.length >= 3) {
                dept = deptMapper.selectDeptById(Long.parseLong(deptId[2]));
            }else {
                if (deptId[deptId.length - 1].equals("0")) {
                    // 最高级部门
                    dept = deptMapper.selectDeptById(100L);
                }else {
                    dept = deptMapper.selectDeptById(Long.parseLong(deptId[deptId.length - 1]));
                }
            }
            if (i == fillIds.length - 1) {
                deptName = deptName + dept.getDeptName();
                ids = ids + dept.getDeptId();
            } else {
                deptName = deptName + dept.getDeptName() + ",";
                ids = ids + dept.getDeptId() + ",";
            }
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysDept de = deptMapper.selectDeptById(loginUser.getDeptId());
        inspectIssued.setIssuedUnit(de.getDeptName());
        inspectIssued.setIssuedUnitId(de.getDeptId());
        inspectIssued.setUnits(deptName);
        inspectIssued.setUnitId(ids);
        inspectIssued.setStatus(1);
        inspectIssued.setDelFlag(0);
        inspectIssued.setCreateTime(DateUtils.getNowDate());
        inspectIssuedMapper.insertInspectIssued(inspectIssued);

        // 填入检查任务表
        InspectTask task = new InspectTask();
        task.setIssuedId(inspectIssued.getId());
        task.setInformant(inspectIssued.getFillId());
        return inspectTaskService.insertInspectTask(task);
    }

    /**
     * 修改检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    @Override
    public int updateInspectIssued(InspectIssued inspectIssued)
    {
        inspectIssued.setUpdateTime(DateUtils.getNowDate());
        return inspectIssuedMapper.updateInspectIssued(inspectIssued);
    }

    /**
     * 批量删除检查下发
     * 
     * @param ids 需要删除的检查下发主键
     * @return 结果
     */
    @Override
    public int deleteInspectIssuedByIds(Long[] ids)
    {
        return inspectIssuedMapper.deleteInspectIssuedByIds(ids);
    }

    /**
     * 删除检查下发信息
     * 
     * @param id 检查下发主键
     * @return 结果
     */
    @Override
    public int deleteInspectIssuedById(Long id)
    {
        return inspectIssuedMapper.deleteInspectIssuedById(id);
    }
}
