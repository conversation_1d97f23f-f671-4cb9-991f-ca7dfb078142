package com.tocc.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.gson.Gson;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.http.HttpUtils;
import com.tocc.common.utils.poi.WordTemplateUtils;
import com.tocc.domain.dto.*;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.EmergencyEventDetailVO;
import com.tocc.domain.vo.YkTokenVO;
import com.tocc.mapper.EmergencyEventMapper;
import com.tocc.mapper.EmergencyEventRoadTrafficMapper;
import com.tocc.mapper.EmergencyEventWaterwayTrafficMapper;
import com.tocc.service.IEmergencyEventService;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.service.IAlarmService;
import com.tocc.system.service.ISysUserService;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.utils.HttpClientUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 应急事件Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class EmergencyEventServiceImpl implements IEmergencyEventService {

    public static final Logger log = LoggerFactory.getLogger(EmergencyEventServiceImpl.class);

    @Autowired
    private EmergencyEventMapper emergencyEventMapper;

    @Autowired
    private EmergencyEventRoadTrafficMapper roadTrafficMapper;

    @Autowired
    private EmergencyEventWaterwayTrafficMapper waterwayTrafficMapper;

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询应急事件
     *
     * @param eventId 应急事件主键
     * @return 应急事件
     */
    @Override
    public EmergencyEventVO selectEmergencyEventByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }
        return convertToVO(dto);
    }

    /**
     * 查询应急事件详情（包含扩展信息）
     *
     * @param eventId 应急事件主键
     * @return 应急事件详情
     */
    @Override
    public EmergencyEventDetailVO selectEmergencyEventDetailByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }

        EmergencyEventDetailVO detailVO = new EmergencyEventDetailVO();
        BeanUtils.copyProperties(convertToVO(dto), detailVO);

        // 根据事件类型查询扩展信息
        if ("1".equals(dto.getEventType())) { // 道路交通事故
            EmergencyEventRoadTrafficDTO roadTrafficDTO = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
            if (roadTrafficDTO != null) {
                detailVO.setRoadSectionCode(roadTrafficDTO.getRoadSectionCode());
                detailVO.setStartStakeNumber(roadTrafficDTO.getStartStakeNumber());
                detailVO.setEndStakeNumber(roadTrafficDTO.getEndStakeNumber());
                detailVO.setDirection(roadTrafficDTO.getDirection());
                detailVO.setTrafficAffected(roadTrafficDTO.getTrafficAffected());
                detailVO.setVehicleType(roadTrafficDTO.getVehicleType());
                detailVO.setEstimatedRecoveryTime(roadTrafficDTO.getEstimatedRecoveryTime());
                detailVO.setRoadCasualtySituation(roadTrafficDTO.getCasualtySituation());
                detailVO.setImpactTrend(roadTrafficDTO.getImpactTrend());
                // TODO: 设置字典名称
            }
        } else if ("2".equals(dto.getEventType())) { // 水路交通事故
            EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
            if (waterwayTrafficDTO != null) {
                detailVO.setWaterwayName(waterwayTrafficDTO.getWaterwayName());
                detailVO.setShipName(waterwayTrafficDTO.getShipName());
                detailVO.setShipType(waterwayTrafficDTO.getShipType());
                detailVO.setShipTonnage(waterwayTrafficDTO.getShipTonnage());
                detailVO.setWaterwayCasualtySituation(waterwayTrafficDTO.getCasualtySituation());
                detailVO.setCargoInfo(waterwayTrafficDTO.getCargoInfo());
                detailVO.setEnvironmentalImpact(waterwayTrafficDTO.getEnvironmentalImpact());
                // TODO: 设置字典名称
            }
        }

        return detailVO;
    }

    /**
     * 查询应急事件列表
     *
     * @param emergencyEvent 应急事件
     * @return 应急事件
     */
    @Override
    public List<EmergencyEventVO> selectEmergencyEventList(EmergencyEventDTO emergencyEvent) {
        // 从SecurityUtils获取当前用户ID
        String currentUserId = getCurrentUserId();

        // 创建查询参数，只查询当前用户作为上报人或填报人的事件
        EmergencyEventDTO queryParam = new EmergencyEventDTO();
        // 复制原有查询条件
        BeanUtils.copyProperties(emergencyEvent, queryParam);
        // 设置当前用户ID用于权限过滤
        queryParam.setReporterId(currentUserId);

        // 直接返回包含用户详细信息的VO列表
        return emergencyEventMapper.selectEmergencyEventListByUser(queryParam);
    }

    /**
     * 新增应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 生成事件ID
        String eventId = UUID.randomUUID().toString();

        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setEventId(eventId);
        Long time = System.currentTimeMillis() / 1000;
        eventDTO.setCreateTime(time);
        // 设置创建者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setCreaterId(currentUserId);

        // 如果没有指定填报人，使用当前用户
        if (createDTO.getReporterId() == null) {
            eventDTO.setReporterId(currentUserId);
        }
        // TODO: 辅助决策、预案与事件等级判别说明、关联预案
        eventDTO.setEventLevel("2");
        eventDTO.setEmerPlanId("054908b4c5de4b71906875c7fe34a47c");
        eventDTO.setPlanLevelJudgment("根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的Ⅱ级及以上公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动Ⅱ级应急响应。本次事件涉及泉南高速柳州段因山体塌方造成交通中断，伴随槽罐车粗苯泄漏和多车连环事故，抢险难度大、处置时间长，符合Ⅱ级响应启动条件。");
        eventDTO.setDecisionSupport("该事故发生在由广西高速公路管理有限公司柳州分公司负责的高速公路路段，已判定为重大公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合Ⅱ级响应启动条件，建议启动Ⅱ级应急响应，由自治区交通运输厅统一指挥和调度。推荐派遣危化品处置专家、隧道工程专家及应急救援专家共同参与，确保高效处置。\n" +
                "根据事故现场山体塌方、危化品粗苯泄漏及多车人员被困等特点，需快速开展清障、救援和危化品转运处置工作。建议调配：挖掘机2台、装载机2台、运输车4辆；消防车1辆、救护车2辆、通讯保障车1辆；空载苯槽车1辆、单兵系统1套、无人机设备1套；具体配置可视现场情况动态调整。");
        // 插入主表
        int result = emergencyEventMapper.insertEmergencyEvent(eventDTO);

        if (result > 0) {
            // 根据事件类型插入扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setId(UUID.randomUUID().toString());
                roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                roadTrafficDTO.setCreaterId(currentUserId);
                roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                waterwayTrafficDTO.setCreaterId(currentUserId);
                waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
            }

            // 创建告警记录
            createEmergencyEventAlarm(createDTO, eventDTO);

            // 发送短信通知上报人
            sendSmsToSubmitter(createDTO, eventDTO);
        }

        return result;
    }

    /**
     * 修改应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setUpdateTime(System.currentTimeMillis() / 1000);
        // 设置更新者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setUpdaterId(currentUserId);

        // 更新主表
        int result = emergencyEventMapper.updateEmergencyEvent(eventDTO);

        if (result > 0) {
            String eventId = eventDTO.getEventId();

            // 根据事件类型更新扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                // 先删除原有扩展数据
                waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);

                // 插入或更新道路交通事故扩展数据
                EmergencyEventRoadTrafficDTO existingRoadTraffic = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingRoadTraffic != null) {
                    roadTrafficDTO.setId(existingRoadTraffic.getId());
                    roadTrafficMapper.updateEmergencyEventRoadTraffic(roadTrafficDTO);
                } else {
                    roadTrafficDTO.setId(UUID.randomUUID().toString());
                    roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
                }
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                // 先删除原有扩展数据
                roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);

                // 插入或更新水路交通事故扩展数据
                EmergencyEventWaterwayTrafficDTO existingWaterwayTraffic = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingWaterwayTraffic != null) {
                    waterwayTrafficDTO.setId(existingWaterwayTraffic.getId());
                    waterwayTrafficMapper.updateEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                } else {
                    waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                    waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                }
            }
        }

        return result;
    }

    /**
     * 批量删除应急事件
     *
     * @param eventIds 需要删除的应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventIds(String[] eventIds) {
        // 删除扩展表数据
        for (String eventId : eventIds) {
            roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
            waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);
        }

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventIds(eventIds);
    }

    /**
     * 删除应急事件信息
     *
     * @param eventId 应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventId(String eventId) {
        // 删除扩展表数据
        roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
        waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventId(eventId);
    }

    /**
     * 更新事件状态
     *
     * @param eventId 事件ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public int updateEventStatus(String eventId, String status) {
        Long updateTime = System.currentTimeMillis() / 1000;
        // 获取当前登录用户ID
        String updaterId = getCurrentUserId();
        return emergencyEventMapper.updateEventStatus(eventId, status, updaterId, updateTime);
    }

    /**
     * 根据事件类型和时间范围查询事件列表
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByTypeAndTime(String eventType, Long startTime, Long endTime) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByTypeAndTime(eventType, startTime, endTime);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 根据状态查询事件列表
     *
     * @param status 状态
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByStatus(String status) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByStatus(status);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 统计应急事件数量
     *
     * @param emergencyEvent 查询条件
     * @return 统计数量
     */
    @Override
    public int countEmergencyEvent(EmergencyEventDTO emergencyEvent) {
        return emergencyEventMapper.countEmergencyEvent(emergencyEvent);
    }

    @Override
    public void downloadNotice(HttpServletResponse response, String eventId) {
        //查询数据
        EmergencyEventDetailVO eventDto = this.selectEmergencyEventDetailByEventId(eventId);
        if (ObjectUtil.isNull(eventDto)) {
            throw new ServiceException("没有找到符合条件的应急事件");
        }
        //准备模板数据
        Map<String, String> templateData = prepareTemplateData(eventDto);
        try {
            //从resources加载模板并处理
            InputStream is = getClass().getClassLoader().getResourceAsStream("templates/notice_template.docx");
            //设置响应头
            String fileName = URLEncoder.encode("关于成立应对" + eventDto.getEventTitle() + "应急处置工作领导小组的通知", "UTF-8") + ".docx";
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            XWPFDocument document = new XWPFDocument(is);
            //处理文档内容
            WordTemplateUtils.processDocument(document, templateData);
            //写入响应流
            document.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 将DTO转换为VO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private EmergencyEventVO convertToVO(EmergencyEventDTO dto) {
        EmergencyEventVO vo = new EmergencyEventVO();
        BeanUtils.copyProperties(dto, vo);

        // TODO: 设置字典名称、用户名称等
        // 可以在这里调用字典服务和用户服务来获取名称

        return vo;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前用户ID
     */
    private String getCurrentUserId() {
        try {
            // 使用SecurityUtils获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            return userId != null ? userId.toString() : null;
        } catch (Exception e) {
            // 如果获取失败，返回null或默认值
            return null;
        }
    }

    /**
     * 创建应急事件告警
     *
     * @param createDTO 应急事件创建DTO（包含完整数据）
     * @param eventDTO 应急事件DTO（主表数据）
     */
    private void createEmergencyEventAlarm(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警标题：xx行政辖区发生xx事件
            String alarmTitle = buildAlarmTitle(createDTO);
            alarmInfo.setAlarmTitle(alarmTitle);

            alarmInfo.setAlarmType("2"); // 应急类型
            alarmInfo.setAlarmSubtype("2"); // 新事件子类型
            alarmInfo.setAlarmLevel(mapEventLevelToAlarmLevel(eventDTO.getEventLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO);
            alarmInfo.setAlarmContent(alarmContent);

            alarmInfo.setSourceId(eventDTO.getEventId());
            alarmInfo.setSourceType("event");

            // 设置行政辖区信息
            alarmInfo.setAdministrativeAreaId(eventDTO.getAdministrativeAreaId());
            alarmInfo.setAdministrativeArea(eventDTO.getAdministrativeArea());

            // 通过填报人ID查询用户信息获取部门信息
            String reporterId = eventDTO.getReporterId();
            if (reporterId != null) {
                try {
                    Long userId = Long.parseLong(reporterId);
                    SysUser reporter = userService.selectUserById(userId);
                    if (reporter != null && reporter.getDept() != null) {
                        alarmInfo.setOrgId(reporter.getDeptId().toString());
                        alarmInfo.setOrgName(reporter.getDept().getDeptName());
                    } else {
                        // 如果查询不到部门信息，使用默认值
                        alarmInfo.setOrgId(reporterId);
                        alarmInfo.setOrgName("未知部门");
                    }
                } catch (NumberFormatException e) {
                    // 如果填报人ID不是数字，使用默认值
                    alarmInfo.setOrgId(reporterId);
                    alarmInfo.setOrgName("未知部门");
                }
            } else {
                // 如果没有填报人ID，使用当前用户的部门信息
                Long currentUserId = SecurityUtils.getUserId();
                Long currentDeptId = SecurityUtils.getDeptId();
                alarmInfo.setOrgId(currentDeptId != null ? currentDeptId.toString() : "");
                alarmInfo.setOrgName("当前用户部门");
            }


            // 调用告警服务创建告警
            alarmService.insertAlarmInfo(alarmInfo);
        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建应急事件告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警标题
     */
    private String buildAlarmTitle(EmergencyEventCreateDTO createDTO) {
        String administrativeArea = createDTO.getAdministrativeArea();
        String eventTypeName = getEventTypeName(createDTO.getEventType());

        // 格式：xx行政辖区发生xx事件
        return String.format("%s发生%s",
                administrativeArea != null ? administrativeArea : "未知区域",
                eventTypeName != null ? eventTypeName : "突发事件");
    }

    /**
     * 构建告警内容
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警内容
     */
    private String buildAlarmContent(EmergencyEventCreateDTO createDTO) {
        StringBuilder content = new StringBuilder();

        // 发生时间
        if (createDTO.getOccurTime() != null) {
            content.append(formatTimestampToChinese(createDTO.getOccurTime()));
        }

        // 事故地址
        if (createDTO.getDetailedAddress() != null) {
            content.append("，在").append(createDTO.getDetailedAddress());
        }

        // 事件类型和事故类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }

        // 事件描述（原因）
        if (createDTO.getEventDescription() != null) {
            content.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficInfoText(content, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficInfoText(content, createDTO);
        }

        content.append("。");
        return content.toString();
    }

    /**
     * 将事件级别映射为告警级别
     *
     * @param eventLevel 事件级别
     * @return 告警级别
     */
    private String mapEventLevelToAlarmLevel(String eventLevel) {
        if (eventLevel == null) {
            return "1"; // 默认一般级别
        }

        switch (eventLevel) {
            case "1": // 事件Ⅰ级(特别重大)
                return "4"; // 告警严重
            case "2": // 事件Ⅱ级(重大)
                return "3"; // 告警紧急
            case "3": // 事件Ⅲ级(较大)
                return "2"; // 告警重要
            case "4": // 事件Ⅳ级(一般)
                return "1"; // 告警一般
            default:
                return "1"; // 默认一般级别
        }
    }

    /**
     * 添加道路交通事故扩展信息（文本格式）
     *
     * @param content 内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    content.append("，预计于").append(formatTimestampToChinese(createDTO.getEstimatedRecoveryTime())).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（文本格式）
     *
     * @param content 内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    /**
     * 格式化时间戳
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的时间字符串
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return timestamp.toString();
        }
    }

    /**
     * 格式化时间戳为中文年月日时分秒格式
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的中文时间字符串（如：2025年06月01日17时25分30秒）
     */
    private String formatTimestampToChinese(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒");
            return sdf.format(date);
        } catch (Exception e) {
            return formatTimestamp(timestamp);
        }
    }

    /**
     * 获取事件类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param eventType 事件类型值
     * @return 事件类型名称
     */
    private String getEventTypeName(String eventType) {
        if (eventType == null) {
            return null;
        }
        switch (eventType) {
            case "1":
                return "道路交通事故";
            case "2":
                return "水路交通事故";
            default:
                return "突发事件";
        }
    }

    /**
     * 获取事故类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param accidentType 事故类型值
     * @return 事故类型名称
     */
    private String getAccidentTypeName(String accidentType) {
        if (accidentType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return accidentType;
    }

    /**
     * 获取船舶类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param shipType 船舶类型值
     * @return 船舶类型名称
     */
    private String getShipTypeName(String shipType) {
        if (shipType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return shipType;
    }

    public static void main(String[] args) {
        String token = getYkToken();
        System.out.println(token);
        NewEmerEventSmsDTO dto = new NewEmerEventSmsDTO();
        dto.setDate("2025年06月03日11时22分");
        dto.setLocation("G75兰海高速上行K2012+300");
        dto.setContent("发生一辆小车抛锚");
        smsSend(token, dto);
    }

    private static void smsSend(String token, NewEmerEventSmsDTO dto) {
        // 测试短信发送
        smsSend(token, "18176275992", "测试短信内容");
    }

    /**
     * 发送短信通知上报人
     *
     * @param createDTO 应急事件创建DTO
     * @param eventDTO 应急事件DTO
     */
    private void sendSmsToSubmitter(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            // 获取上报人手机号
            String submitterMobile = getSubmitterMobile(eventDTO.getSubmitterId());
            if (submitterMobile == null || submitterMobile.isEmpty()) {
                log.warn("上报人手机号为空，无法发送短信通知，上报人ID: {}", eventDTO.getSubmitterId());
                return;
            }
            // 构建短信内容
            String smsContent = buildSmsContent(createDTO);

            // 获取token并发送短信
            String token = getYkToken();
            if (token != null) {
                smsSend(token, submitterMobile, smsContent);
                log.info("短信发送成功，手机号: {}, 内容: {}", submitterMobile, smsContent);
            } else {
                log.error("获取短信token失败，无法发送短信");
            }
        } catch (Exception e) {
            // 短信发送失败不影响主业务流程，只记录日志
            log.error("发送短信通知失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上报人手机号
     *
     * @param submitterId 上报人ID
     * @return 手机号
     */
    private String getSubmitterMobile(String submitterId) {
        if (submitterId == null || submitterId.isEmpty()) {
            return null;
        }

        try {
            Long userId = Long.parseLong(submitterId);
            SysUser submitter = userService.selectUserById(userId);
            return submitter != null ? submitter.getPhonenumber() : null;
        } catch (NumberFormatException e) {
            log.error("上报人ID格式错误: {}", submitterId);
            return null;
        }
    }

    /**
     * 构建短信内容（JSON格式）
     *
     * @param createDTO 应急事件创建DTO
     * @return 短信内容（JSON格式）
     */
    private String buildSmsContent(EmergencyEventCreateDTO createDTO) {
        // 构建date字段（格式：2025年06月03日11时22分30秒）
        String dateStr = "";
        if (createDTO.getOccurTime() != null) {
            dateStr = formatTimestampToChinese(createDTO.getOccurTime());
        }

        // 构建location字段
        String locationStr = createDTO.getDetailedAddress() != null ? createDTO.getDetailedAddress() : "";

        // 构建content字段（参考告警内容格式）
        StringBuilder contentBuilder = new StringBuilder();

        // 事件类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            contentBuilder.append("发生一起").append(eventTypeName);
        }

        // 事件描述
        if (createDTO.getEventDescription() != null && !createDTO.getEventDescription().isEmpty()) {
            contentBuilder.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficSmsInfo(contentBuilder, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficSmsInfo(contentBuilder, createDTO);
        }

        // 构建JSON格式的内容
        return String.format("{\"date\":\"%s\",\"location\":\"%s\",\"content\":\"%s\"}",
                            dateStr, locationStr, contentBuilder.toString());
    }

    /**
     * 添加道路交通事故扩展信息（短信格式）
     *
     * @param content 内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    String recoveryTimeStr = formatTimestampToChinese(createDTO.getEstimatedRecoveryTime());
                    content.append("，预计于").append(recoveryTimeStr).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null && !createDTO.getRoadCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（短信格式）
     *
     * @param content 内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null && !createDTO.getShipName().isEmpty()) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null && !createDTO.getCargoInfo().isEmpty()) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null && !createDTO.getWaterwayCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null && !createDTO.getEnvironmentalImpact().isEmpty()) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    private static void smsSend(String token, String mobile, String content) {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend";
        Map<String, String> head = new HashMap<>();
        head.put("AuthorizationType", "other");
        head.put("Authorization", token);
        Map<String, String> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("content", content);
        body.put("signName", "智慧高速云控平台");
        String response = HttpClientUtils.postWithBody(10000, url, head, body);
        log.info("短信发送响应: {}", response);
    }

    private static String getYkToken() {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken";
        Map<String, String> body = new HashMap<>();
        body.put("appId", "fb9686ae-8706-46ce-926d-23ddfbc010e9");
        body.put("appSecret", "WTKkpkH*wAyFCKgbbfNz$sMnbuqd#Wj#");
        String response = HttpClientUtils.postWithBody(10000, url, null, body);
        YkTokenVO vo = new Gson().fromJson(response, YkTokenVO.class);
        if (vo.getCode() == 1) {
            return vo.getToken();
        }
        return null;
    }

    /**
     * 导出通知模板替换数据
     */
    private Map<String, String> prepareTemplateData(EmergencyEventDetailVO vo) {
        Map<String, String> data = new HashMap<>();
        data.put("${occurTime}", formatTimestampToChinese(vo.getOccurTime()));
        String noticeContent = buildNoticeContent(vo);
        data.put("${eventDesc}", noticeContent);
        //获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNotNull(loginUser)) {
            SysUser currentUser = loginUser.getUser();
            if (StringUtils.isNotNull(currentUser) && StringUtils.isNotNull(currentUser.getDept())) {
                data.put("${unitName}", currentUser.getDept().getDeptName());
            }
        }
        String time = DateUtil.format(DateUtil.date(), "yyyy年MM月dd日");
        data.put("${time}", time);
        //TODO 领导小组组成人员数据获取替换
        return data;
    }

    /**
     * 构建通知突发事件描述
     *
     * @param vo
     * @return 突发事件描述
     */
    private String buildNoticeContent(EmergencyEventDetailVO vo) {
        StringBuilder content = new StringBuilder();
        //事故地址
        if (vo.getDetailedAddress() != null) {
            content.append("在").append(vo.getDetailedAddress());
        }
        //事件类型和事故类型
        String eventTypeName = getEventTypeName(vo.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }
        //事件描述（原因）
        if (vo.getEventDescription() != null) {
            content.append("，").append(vo.getEventDescription());
        }
        //根据事件类型添加扩展信息
        if ("1".equals(vo.getEventType())) { //道路交通事故
            //是否影响通行
            if (vo.getTrafficAffected() != null) {
                if ("Y".equals(vo.getTrafficAffected())) {
                    content.append("。该事故影响通行");
                    // 预计恢复时间
                    if (vo.getEstimatedRecoveryTime() != null) {
                        content.append("，预计于").append(formatTimestampToChinese(vo.getEstimatedRecoveryTime())).append("恢复通行");
                    }
                } else {
                    content.append("。该事故不影响通行");
                }
            }
            //人员伤亡情况
            if (vo.getRoadCasualtySituation() != null) {
                content.append("，目前").append(vo.getRoadCasualtySituation());
            }
        } else if ("2".equals(vo.getEventType())) { //水路交通事故
            //船舶信息
            if (vo.getShipName() != null) {
                content.append("，涉事船舶为").append(vo.getShipName());
                //船舶吨位
                if (vo.getShipTonnage() != null) {
                    content.append("（").append(vo.getShipTonnage()).append("吨）");
                }
            }
            //货物信息
            if (vo.getCargoInfo() != null) {
                content.append("，载有").append(vo.getCargoInfo());
            }
            //人员伤亡情况
            if (vo.getWaterwayCasualtySituation() != null) {
                content.append("，目前").append(vo.getWaterwayCasualtySituation());
            }
            //环境影响
            if (vo.getEnvironmentalImpact() != null) {
                content.append("，").append(vo.getEnvironmentalImpact());
            }
        }
        return content.toString();
    }
}
