package com.tocc.web.emController;

import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.domain.vo.TaskExecutionResultVO;
import com.tocc.quartz.task.InfoUpdateTimeoutCheckTask;
import com.tocc.service.ITaskTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 定时任务测试Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "定时任务测试")
@RestController
@RequestMapping("/task/test")
public class TaskTestController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TaskTestController.class);
    
    @Autowired
    private InfoUpdateTimeoutCheckTask infoUpdateTimeoutCheckTask;

    @Autowired
    private ITaskTestService taskTestService;
    
    /**
     * 手动执行信息更新超时检查任务
     */
    @ApiOperation("手动执行信息更新超时检查任务")
    @PreAuthorize("@ss.hasPermi('task:test:execute')")
    @PostMapping("/timeout-check")
    public AjaxResult executeTimeoutCheck(
            @ApiParam(value = "任务参数", required = false)
            @RequestParam(required = false) String params) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始手动执行信息更新超时检查任务，参数：{}", params);
            
            // 执行定时任务
            infoUpdateTimeoutCheckTask.execute(params);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("信息更新超时检查任务执行完成，耗时：{}ms", duration);
            
            return success("任务执行成功，耗时：" + duration + "ms");
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("信息更新超时检查任务执行失败，耗时：{}ms", duration, e);
            
            return error("任务执行失败：" + e.getMessage() + "，耗时：" + duration + "ms");
        }
    }
    
    /**
     * 执行信息更新超时检查任务（详细版本）
     */
    @ApiOperation("执行信息更新超时检查任务（详细版本）")
    @PreAuthorize("@ss.hasPermi('task:test:execute')")
    @PostMapping("/timeout-check-details")
    public AjaxResult executeTimeoutCheckWithDetails(
            @ApiParam(value = "任务参数", required = false)
            @RequestParam(required = false) String params) {

        try {
            log.info("开始执行详细版本的信息更新超时检查任务，参数：{}", params);

            TaskExecutionResultVO result = taskTestService.executeTimeoutCheckWithDetails(params);

            if (result.getSuccess()) {
                return success(result);
            } else {
                return error(result.getMessage(), result);
            }

        } catch (Exception e) {
            log.error("执行详细版本的信息更新超时检查任务失败", e);
            return error("任务执行失败：" + e.getMessage());
        }
    }

    /**
     * 检查任务执行环境
     */
    @ApiOperation("检查任务执行环境")
    @PreAuthorize("@ss.hasPermi('task:test:status')")
    @GetMapping("/environment-check")
    public AjaxResult checkTaskEnvironment() {
        try {
            log.info("开始检查任务执行环境");

            TaskExecutionResultVO result = taskTestService.checkTaskEnvironment();

            if (result.getSuccess()) {
                return success(result);
            } else {
                return error(result.getMessage(), result);
            }

        } catch (Exception e) {
            log.error("检查任务执行环境失败", e);
            return error("环境检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务执行状态信息
     */
    @ApiOperation("获取任务执行状态信息")
    @PreAuthorize("@ss.hasPermi('task:test:status')")
    @GetMapping("/status")
    public AjaxResult getTaskStatus() {
        try {
            // 这里可以添加一些状态检查逻辑
            // 比如检查相关服务是否正常、数据库连接是否正常等

            return success("任务相关服务状态正常");

        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            return error("获取任务状态失败：" + e.getMessage());
        }
    }
}
