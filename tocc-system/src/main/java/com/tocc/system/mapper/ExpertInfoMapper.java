package com.tocc.system.mapper;

import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;

import java.util.Date;
import java.util.List;

/**
 * 专家信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExpertInfoMapper {
    
    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    int insertExpertInfo(ExpertInfoDTO expertInfo);
    
    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    int updateExpertInfo(ExpertInfoDTO expertInfo);
    
    /**
     * 根据用户ID删除专家信息
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteExpertInfoByUserId(Long userId);
    
    /**
     * 根据用户ID查询专家信息
     * 
     * @param userId 用户ID
     * @return 专家信息
     */
    ExpertInfoDTO selectExpertInfoByUserId(Long userId);
    
    /**
     * 查询超时的专家信息
     * 
     * @param timeoutTime 超时时间点
     * @return 超时的专家列表
     */
    List<ExpertInfoVO> selectTimeoutExperts(Date timeoutTime);
    
    /**
     * 批量删除专家信息
     * 
     * @param userIds 用户ID数组
     * @return 结果
     */
    int deleteExpertInfoByUserIds(Long[] userIds);
}
