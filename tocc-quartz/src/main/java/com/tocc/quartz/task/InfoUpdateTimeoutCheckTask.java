package com.tocc.quartz.task;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.service.IAlarmService;
import com.tocc.service.IRescueTeamService;
import com.tocc.service.IWarehouseService;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.system.service.ISysUserService;
import com.tocc.system.service.ISysDeptService;
import com.tocc.system.service.IExpertService;
import com.tocc.system.domain.vo.ExpertInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * 信息更新超时检查定时任务
 * 
 * <AUTHOR>
 */
@Component("infoUpdateTimeoutCheckTask")
public class InfoUpdateTimeoutCheckTask {
    
    private static final Logger log = LoggerFactory.getLogger(InfoUpdateTimeoutCheckTask.class);
    
    @Autowired
    private IAlarmService alarmService;
    
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IEmPrePlanService prePlanService;
    
    @Autowired
    private IRescueTeamService rescueTeamService;
    
    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IExpertService expertService;
    
    /**
     * 执行信息更新超时检查
     * 
     * @param params 参数（可选）
     */
    public void execute(String params) {
        log.info("开始执行信息更新超时检查任务，参数：{}", params);
        
        try {
            // 检查应急预案
            checkPrePlanTimeout();

            // 检查救援队伍
            checkRescueTeamTimeout();

            // 检查物资仓库
            checkWarehouseTimeout();

            // 检查专家信息
            checkExpertTimeout();

            log.info("信息更新超时检查任务执行完成");
        } catch (Exception e) {
            log.error("信息更新超时检查任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 检查应急预案更新超时
     */
    private void checkPrePlanTimeout() {
        // 获取超时阈值（分钟）
        int timeoutMinutes = getTimeoutMinutes("应急预案更新阈值", 1);
        
        // 计算超时时间点
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        // 查询超时的预案
        List<EmPrePlanVO> timeoutPlans = prePlanService.selectTimeoutPlans(timeoutTime);
        
        log.info("发现{}个超时的应急预案", timeoutPlans.size());
        
        // 为每个超时预案创建告警
        for (EmPrePlanVO plan : timeoutPlans) {
            createTimeoutAlarm("3", plan.getId(), plan.getPlanName(),
                              plan.getLastCheckTime(), timeoutMinutes, plan.getCreator());
        }
    }
    
    /**
     * 检查救援队伍更新超时
     */
    private void checkRescueTeamTimeout() {
        int timeoutMinutes = getTimeoutMinutes("救援队伍更新阈值", 1);
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        List<RescueTeamVO> timeoutTeams = rescueTeamService.selectTimeoutTeams(timeoutTime);
        
        log.info("发现{}个超时的救援队伍", timeoutTeams.size());
        
        for (RescueTeamVO team : timeoutTeams) {
            createTimeoutAlarm("7", team.getId(), team.getTeamName(),
                              team.getUpdateTime(), timeoutMinutes, team.getCreator());
        }
    }
    
    /**
     * 检查物资仓库更新超时
     */
    private void checkWarehouseTimeout() {
        int timeoutMinutes = getTimeoutMinutes("物资仓库更新阈值", 1);
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

        List<WarehouseVO> timeoutWarehouses = warehouseService.selectTimeoutWarehouses(timeoutTime);

        log.info("发现{}个超时的物资仓库", timeoutWarehouses.size());

        for (WarehouseVO warehouse : timeoutWarehouses) {
            createTimeoutAlarm("4", warehouse.getId(), warehouse.getWarehouseName(),
                              warehouse.getUpdateTime(), timeoutMinutes, warehouse.getCreator());
        }
    }

    /**
     * 检查专家信息更新超时
     */
    private void checkExpertTimeout() {
        try {
            // 获取专家信息更新超时阈值（分钟）
            int timeoutMinutes = getTimeoutMinutes("应急通讯录更新阈值", 1);

            // 计算超时时间点
            Date timeoutTime = DateUtils.addMinutes(new Date(), -timeoutMinutes);

            // 查询超时的专家信息
            List<ExpertInfoVO> timeoutExperts = expertService.selectTimeoutExperts(timeoutTime);

            log.info("检查专家信息更新超时，超时阈值：{}分钟，发现超时专家：{}个", timeoutMinutes, timeoutExperts.size());

            // 为每个超时专家创建告警
            for (ExpertInfoVO expert : timeoutExperts) {
                // 检查是否已存在相同告警
                if (!alarmService.existsTimeoutAlarm("5", expert.getUserId().toString())) { // 5=应急通讯录
                    createTimeoutAlarm("5", expert.getUserId().toString(),
                                     expert.getNickName() != null ? expert.getNickName() : expert.getUserName(),
                                     expert.getUpdateTime(), timeoutMinutes, expert.getCreateBy());
                }
            }

        } catch (Exception e) {
            log.error("检查专家信息更新超时失败", e);
        }
    }
    
    /**
     * 获取超时阈值（分钟）
     */
    private int getTimeoutMinutes(String dictLabel, int defaultValue) {
        try {
            String dictValue = dictDataService.selectDictLabel("info_update_timeout", dictLabel);
            if (StringUtils.isNotEmpty(dictValue)) {
                return Integer.parseInt(dictValue);
            }
        } catch (Exception e) {
            log.warn("获取字典值失败，使用默认值：{}", defaultValue, e);
        }
        return defaultValue;
    }
    
    /**
     * 创建超时告警
     */
    private void createTimeoutAlarm(String infoType, String infoId, String infoName,
                                   Date lastUpdateTime, int timeoutMinutes, String creatorName) {
        
        // 检查是否已经存在相同的告警（避免重复告警）
        if (alarmService.existsTimeoutAlarm(infoType, infoId)) {
            log.debug("{}[{}]已存在超时告警，跳过", infoType, infoName);
            return;
        }
        
        // 计算超时分钟数
        long overdueMinutes = ChronoUnit.MINUTES.between(
            lastUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        ) - timeoutMinutes;
        
        // 获取中文类型名称用于显示
        String infoTypeName = getInfoTypeName(infoType);

        // 构建告警标题
        String alarmTitle = String.format("%s信息更新超时", infoTypeName);

        // 构建告警内容
        String alarmContent = String.format(
            "%s\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。请及时更新相关信息以确保数据准确性。",
            infoTypeName, infoName, timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, lastUpdateTime)
        );
        
        // 创建告警记录
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmId(generateAlarmId()); // 生成唯一告警ID
        alarmInfo.setAlarmType("2"); // 信息更新超时对应字典值2
        alarmInfo.setAlarmSubtype(getAlarmSubtype(infoType)); // 设置告警子类型
        alarmInfo.setAlarmTitle(alarmTitle);
        alarmInfo.setAlarmContent(alarmContent);
        alarmInfo.setAlarmLevel("2"); // 中等级别
        alarmInfo.setSourceType(infoType); // 源数据类型
        alarmInfo.setSourceId(infoId); // 源数据ID

        // 获取创建人的部门信息
        String[] orgInfo = getCreatorOrgInfo(creatorName);
        alarmInfo.setOrgId(orgInfo[0]); // 设置组织ID
        alarmInfo.setOrgName(orgInfo[1]); // 设置组织名称
        alarmInfo.setAdministrativeAreaId("default"); // 设置默认行政区划ID
        alarmInfo.setAlarmTime(new Date());
        alarmInfo.setStatus("0"); // 未处理
        alarmInfo.setCreateBy("system"); // 系统创建
        alarmInfo.setCreateTime(new Date());
        
        alarmService.createAlarm(alarmInfo);
        
        log.info("创建{}更新超时告警：{}", infoTypeName, infoName);
    }

    /**
     * 根据信息类型获取告警子类型
     *
     * @param infoType 信息类型
     * @return 告警子类型
     */
    private String getAlarmSubtype(String infoType) {
        switch (infoType) {
            case "3": // 应急预案
                return "4"; // 预案更新超时
            case "4": // 应急物资
                return "5"; // 物资更新超时
            case "5": // 专家信息
                return "6"; // 专家更新超时
            case "7": // 应急救援队伍
                return "6"; // 通讯录更新超时
            default:
                return "4"; // 默认为预案更新超时
        }
    }

    /**
     * 根据信息类型字典值获取中文名称
     *
     * @param infoType 信息类型字典值
     * @return 中文名称
     */
    private String getInfoTypeName(String infoType) {
        switch (infoType) {
            case "3":
                return "应急预案";
            case "4":
                return "应急物资";
            case "5":
                return "应急通讯录";
            case "7":
                return "应急救援队伍";
            default:
                return "未知类型";
        }
    }


    /**
     * 根据创建人获取组织信息
     *
     * @param creatorName 创建人姓名
     * @return 组织信息数组 [组织ID, 组织名称]
     */
    private String[] getCreatorOrgInfo(String creatorName) {
        String[] defaultOrgInfo = {"default", "默认部门"};

        if (StringUtils.isEmpty(creatorName)) {
            return defaultOrgInfo;
        }

        try {
            // 根据创建人姓名查询用户信息
            SysUser user = userService.selectUserByUserName(creatorName);
            if (user == null || user.getDeptId() == null) {
                log.warn("未找到创建人[{}]的用户信息或部门信息", creatorName);
                return defaultOrgInfo;
            }

            // 根据部门ID查询部门信息
            SysDept dept = deptService.selectDeptById(user.getDeptId());
            if (dept == null) {
                log.warn("未找到部门信息，部门ID: {}", user.getDeptId());
                return defaultOrgInfo;
            }

            return new String[]{dept.getDeptId().toString(), dept.getDeptName()};

        } catch (Exception e) {
            log.error("获取创建人[{}]的组织信息失败", creatorName, e);
            return defaultOrgInfo;
        }
    }

    /**
     * 生成唯一的告警ID
     *
     * @return 告警ID
     */
    private String generateAlarmId() {
        return IdUtils.fastUUID();
    }
}
