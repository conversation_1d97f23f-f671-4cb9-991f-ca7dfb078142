# 公路编号树形接口使用指南

## 接口概述

新增了公路编号的树形结构接口，按照道路类型对公路编号进行分层展示，便于前端组件（如树形选择器、级联选择器等）使用。

## 接口信息

### 获取公路编号树形结构
```
GET /system/marker/tree
```

**权限要求：** `system:marker:list`

## 树形结构说明

### 层级结构
```
├── 道路类型节点（第一层）
│   ├── 公路编号节点（第二层 - 叶子节点）
│   ├── 公路编号节点（第二层 - 叶子节点）
│   └── ...
├── 道路类型节点（第一层）
│   └── ...
```

### 节点类型
1. **道路类型节点** (`nodeType: "type"`)
   - 高速公路
   - 国省干道
   - 县乡公路
   - 城市道路
   - 未分类

2. **公路编号节点** (`nodeType: "marker"`)
   - 具体的公路编号信息

## 响应格式

### 成功响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "type_2",
      "label": "国省干道",
      "value": "type_2",
      "nodeType": "type",
      "roadType": "2",
      "isLeaf": false,
      "children": [
        {
          "id": "marker_1",
          "label": "G72 - 泉南高速",
          "value": "marker_1",
          "nodeType": "marker",
          "markerId": 1,
          "code": "G72",
          "name": "泉南高速",
          "allName": "泉南高速公路",
          "isLeaf": true,
          "children": []
        },
        {
          "id": "marker_2",
          "label": "S1 - 南宁绕城",
          "value": "marker_2",
          "nodeType": "marker",
          "markerId": 2,
          "code": "S1",
          "name": "南宁绕城",
          "allName": "南宁绕城高速公路",
          "isLeaf": true,
          "children": []
        }
      ]
    },
    {
      "id": "type_1",
      "label": "高速公路",
      "value": "type_1",
      "nodeType": "type",
      "roadType": "1",
      "isLeaf": false,
      "children": [
        {
          "id": "marker_3",
          "label": "G75 - 兰海高速",
          "value": "marker_3",
          "nodeType": "marker",
          "markerId": 3,
          "code": "G75",
          "name": "兰海高速",
          "allName": "兰州至海口高速公路",
          "isLeaf": true,
          "children": []
        }
      ]
    }
  ]
}
```

## 字段说明

### 通用字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 节点唯一标识 |
| label | String | 节点显示名称 |
| value | String | 节点值（通常与id相同） |
| nodeType | String | 节点类型（type/marker） |
| isLeaf | Boolean | 是否为叶子节点 |
| children | Array | 子节点列表 |

### 道路类型节点特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| roadType | String | 道路类型值 |

### 公路编号节点特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| markerId | Long | 公路编号数据库ID |
| code | String | 公路编号 |
| name | String | 公路简称 |
| allName | String | 公路全称 |

## 使用场景

### 1. 树形选择器
```javascript
// Vue + Element UI 示例
<el-tree
  :data="roadMarkerTree"
  :props="treeProps"
  node-key="id"
  @node-click="handleNodeClick">
</el-tree>

data() {
  return {
    roadMarkerTree: [],
    treeProps: {
      children: 'children',
      label: 'label'
    }
  }
},
methods: {
  async loadTree() {
    const response = await this.$http.get('/system/marker/tree');
    this.roadMarkerTree = response.data;
  },
  handleNodeClick(data) {
    if (data.nodeType === 'marker') {
      console.log('选中公路编号:', data.code, data.name);
      // 处理公路编号选择逻辑
    }
  }
}
```

### 2. 级联选择器
```javascript
// 转换为级联选择器格式
function convertToCascaderOptions(treeData) {
  return treeData.map(typeNode => ({
    value: typeNode.roadType,
    label: typeNode.label,
    children: typeNode.children.map(markerNode => ({
      value: markerNode.markerId,
      label: markerNode.label
    }))
  }));
}
```

### 3. 筛选和搜索
```javascript
// 根据节点类型筛选
function filterByNodeType(tree, nodeType) {
  return tree.filter(node => node.nodeType === nodeType);
}

// 搜索公路编号
function searchMarker(tree, keyword) {
  const results = [];
  tree.forEach(typeNode => {
    const matchedMarkers = typeNode.children.filter(marker => 
      marker.code.includes(keyword) || 
      marker.name.includes(keyword) ||
      marker.allName.includes(keyword)
    );
    if (matchedMarkers.length > 0) {
      results.push({
        ...typeNode,
        children: matchedMarkers
      });
    }
  });
  return results;
}
```

## 请求示例

### cURL
```bash
curl -X GET "http://localhost:8380/system/marker/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### JavaScript/Ajax
```javascript
$.ajax({
  url: '/system/marker/tree',
  type: 'GET',
  success: function(response) {
    if (response.code === 200) {
      console.log('树形数据:', response.data);
      // 处理树形数据
    }
  }
});
```

### Axios
```javascript
axios.get('/system/marker/tree')
  .then(response => {
    const treeData = response.data.data;
    console.log('树形数据:', treeData);
  })
  .catch(error => {
    console.error('获取树形数据失败:', error);
  });
```

## 注意事项

1. **权限控制**：需要 `system:marker:list` 权限才能访问
2. **数据实时性**：树形数据基于当前数据库中的公路编号数据生成
3. **空数据处理**：如果某个道路类型下没有公路编号，该类型节点仍会显示，但children为空数组
4. **未分类处理**：没有设置道路类型的公路编号会归类到"未分类"节点下

## 扩展建议

1. **缓存优化**：可以考虑对树形数据进行缓存，提高响应速度
2. **懒加载**：对于大量数据，可以实现按需加载子节点
3. **自定义排序**：可以添加排序参数，支持按编号、名称等排序
4. **筛选参数**：可以添加筛选参数，只返回特定道路类型的数据

这个树形接口为前端提供了灵活的数据结构，便于实现各种树形组件和选择器！
