package com.tocc.web.emController;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.domain.dto.EmergencyEventCreateDTO;
import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.EmergencyEventDetailVO;
import com.tocc.service.IEmergencyEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 应急事件Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "应急事件管理")
@RestController
@RequestMapping("/emergency/event")
public class EmergencyEventController extends BaseController {
    
    @Autowired
    private IEmergencyEventService emergencyEventService;

    /**
     * 查询应急事件列表
     */
    @ApiOperation("查询应急事件列表")
    @PreAuthorize("@ss.hasPermi('emergency:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmergencyEventDTO emergencyEvent) {
        startPage();
        List<EmergencyEventVO> list = emergencyEventService.selectEmergencyEventList(emergencyEvent);
        return getDataTable(list);
    }

    /**
     * 获取应急事件详细信息
     */
    @ApiOperation("获取应急事件详细信息")
    @PreAuthorize("@ss.hasPermi('emergency:event:query')")
    @GetMapping(value = "/{eventId}")
    public AjaxResult getInfo(@ApiParam(value = "事件ID", required = true) @PathVariable("eventId") String eventId) {
        EmergencyEventDetailVO detailVO = emergencyEventService.selectEmergencyEventDetailByEventId(eventId);
        return success(detailVO);
    }

    /**
     * 新增应急事件
     */
    @ApiOperation("新增应急事件")
    @PreAuthorize("@ss.hasPermi('emergency:event:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody EmergencyEventCreateDTO createDTO) {
        return toAjax(emergencyEventService.insertEmergencyEvent(createDTO));
    }

    /**
     * 修改应急事件
     */
    @ApiOperation("修改应急事件")
    @PreAuthorize("@ss.hasPermi('emergency:event:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EmergencyEventCreateDTO createDTO) {
        return toAjax(emergencyEventService.updateEmergencyEvent(createDTO));
    }

    /**
     * 删除应急事件
     */
    @ApiOperation("删除应急事件")
    @PreAuthorize("@ss.hasPermi('emergency:event:remove')")
    @DeleteMapping("/{eventIds}")
    public AjaxResult remove(@ApiParam(value = "事件ID数组", required = true) @PathVariable String[] eventIds) {
        return toAjax(emergencyEventService.deleteEmergencyEventByEventIds(eventIds));
    }

    /**
     * 更新事件状态
     */
    @ApiOperation("更新事件状态")
    @PreAuthorize("@ss.hasPermi('emergency:event:edit')")
    @PutMapping("/status/{eventId}/{status}")
    public AjaxResult updateStatus(
            @ApiParam(value = "事件ID", required = true) @PathVariable("eventId") String eventId,
            @ApiParam(value = "状态", required = true) @PathVariable("status") String status) {
        return toAjax(emergencyEventService.updateEventStatus(eventId, status));
    }

    /**
     * 根据事件类型和时间范围查询事件列表
     */
    @ApiOperation("根据事件类型和时间范围查询事件列表")
    @PreAuthorize("@ss.hasPermi('emergency:event:list')")
    @GetMapping("/listByTypeAndTime")
    public AjaxResult listByTypeAndTime(
            @ApiParam(value = "事件类型") @RequestParam(required = false) String eventType,
            @ApiParam(value = "开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) Long endTime) {
        List<EmergencyEventVO> list = emergencyEventService.selectEventsByTypeAndTime(eventType, startTime, endTime);
        return success(list);
    }

    /**
     * 根据状态查询事件列表
     */
    @ApiOperation("根据状态查询事件列表")
    @PreAuthorize("@ss.hasPermi('emergency:event:list')")
    @GetMapping("/listByStatus/{status}")
    public AjaxResult listByStatus(@ApiParam(value = "状态", required = true) @PathVariable("status") String status) {
        List<EmergencyEventVO> list = emergencyEventService.selectEventsByStatus(status);
        return success(list);
    }

    /**
     * 统计应急事件数量
     */
    @ApiOperation("统计应急事件数量")
    @PreAuthorize("@ss.hasPermi('emergency:event:list')")
    @GetMapping("/count")
    public AjaxResult count(EmergencyEventDTO emergencyEvent) {
        int count = emergencyEventService.countEmergencyEvent(emergencyEvent);
        return success(count);
    }

    /**
     * 导出应急事件列表
     */
    @ApiOperation("导出应急事件列表")
    @PreAuthorize("@ss.hasPermi('emergency:event:export')")
    @PostMapping("/export")
    public void export(EmergencyEventDTO emergencyEvent) {
        List<EmergencyEventVO> list = emergencyEventService.selectEmergencyEventList(emergencyEvent);
        // TODO: 实现导出功能
    }

    /**
     * 导出通知
     */
    @ApiOperation("导出通知")
    //@PreAuthorize("@ss.hasPermi('emergency:event:downloadNotice')")
    @PostMapping("/downloadNotice")
    public void downloadNotice(HttpServletResponse response, String eventId) {
        emergencyEventService.downloadNotice(response, eventId);
    }

    /**
     * 导出应急事件辅助决策建议word文档
     * @param eventId 事件ID
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('emergency:event:export')")
    @PostMapping("/export/decisionAdvice")
    public void exportEmergencyEventDecisionDocx(@RequestParam String eventId, HttpServletResponse response) {
        emergencyEventService.exportEmergencyEventDecisionDocx(eventId, response);
    }
}
