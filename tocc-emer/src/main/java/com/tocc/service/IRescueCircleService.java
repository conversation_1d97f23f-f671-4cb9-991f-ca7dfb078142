package com.tocc.service;

import com.tocc.domain.vo.RescueCircleVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.domain.vo.RescueTeamVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应急救援圈Service接口
 * 
 * <AUTHOR>
 */
public interface IRescueCircleService {
    
    /**
     * 根据应急事件ID获取救援圈信息
     * 
     * @param eventId 应急事件ID
     * @return 救援圈信息
     */
    RescueCircleVO getRescueCircleByEventId(String eventId);
    
    /**
     * 根据坐标获取救援圈信息
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return 救援圈信息
     */
    RescueCircleVO getRescueCircleByCoordinates(BigDecimal longitude, BigDecimal latitude);
    
    /**
     * 查询指定范围内的仓库
     * 
     * @param longitude 中心点经度
     * @param latitude 中心点纬度
     * @param radiusKm 半径（公里）
     * @return 仓库列表
     */
    List<WarehouseVO> getWarehousesWithinRadius(BigDecimal longitude, BigDecimal latitude, double radiusKm);
    
    /**
     * 查询指定范围内的救援队伍
     * 
     * @param longitude 中心点经度
     * @param latitude 中心点纬度
     * @param radiusKm 半径（公里）
     * @return 救援队伍列表
     */
    List<RescueTeamVO> getRescueTeamsWithinRadius(BigDecimal longitude, BigDecimal latitude, double radiusKm);
    
    /**
     * 计算两点之间的距离（公里）
     * 
     * @param lon1 点1经度
     * @param lat1 点1纬度
     * @param lon2 点2经度
     * @param lat2 点2纬度
     * @return 距离（公里）
     */
    double calculateDistance(double lon1, double lat1, double lon2, double lat2);
}
