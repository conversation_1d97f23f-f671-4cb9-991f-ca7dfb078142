package com.tocc.risk.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.PitfallsMapper;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.service.IPitfallsService;

/**
 * 隐患列Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class PitfallsServiceImpl implements IPitfallsService 
{
    @Autowired
    private PitfallsMapper pitfallsMapper;

    /**
     * 查询隐患列
     * 
     * @param id 隐患列主键
     * @return 隐患列
     */
    @Override
    public Pitfalls selectPitfallsById(Long id)
    {
        return pitfallsMapper.selectPitfallsById(id);
    }

    /**
     * 查询隐患列列表
     * 
     * @param pitfalls 隐患列
     * @return 隐患列
     */
    @Override
    public List<Pitfalls> selectPitfallsList(Pitfalls pitfalls)
    {
        return pitfallsMapper.selectPitfallsList(pitfalls);
    }

    /**
     * 新增隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    @Override
    public int insertPitfalls(Pitfalls pitfalls)
    {
        pitfalls.setDelFlag(0);
        pitfalls.setCreateTime(DateUtils.getNowDate());
        return pitfallsMapper.insertPitfalls(pitfalls);
    }

    /**
     * 修改隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    @Override
    public int updatePitfalls(Pitfalls pitfalls)
    {
        pitfalls.setUpdateTime(DateUtils.getNowDate());
        return pitfallsMapper.updatePitfalls(pitfalls);
    }

    /**
     * 批量删除隐患列
     * 
     * @param ids 需要删除的隐患列主键
     * @return 结果
     */
    @Override
    public int deletePitfallsByIds(Long[] ids)
    {
        return pitfallsMapper.deletePitfallsByIds(ids);
    }

    /**
     * 删除隐患列信息
     * 
     * @param id 隐患列主键
     * @return 结果
     */
    @Override
    public int deletePitfallsById(Long id)
    {
        return pitfallsMapper.deletePitfallsById(id);
    }
}
