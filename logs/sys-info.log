08:35:44.624 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 23025 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
08:35:44.625 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:35:44.626 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:35:45.520 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
08:35:45.521 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:35:45.521 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:35:45.556 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:35:46.680 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:35:47.686 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:35:47.690 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:35:47.690 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:35:47.690 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:35:47.691 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:35:47.691 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:35:47.691 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:35:47.691 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@33c011b1
08:35:48.409 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
08:35:48.712 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:35:48.716 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.226 seconds (JVM running for 4.595)
08:35:50.954 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:35:56.447 [schedule-pool-1] INFO  sys-user - [run,55] - [**********]内网IP[admin][Success][登录成功]
08:54:31.284 [schedule-pool-1] INFO  sys-user - [run,55] - [***********]内网IP[admin][Success][登录成功]
09:27:09.993 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
09:27:10.015 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
09:27:10.015 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
09:27:10.015 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
09:27:10.015 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
09:27:10.017 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:27:10.020 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:27:12.730 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
09:27:12.731 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:27:12.731 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:27:13.830 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
09:27:13.831 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:27:13.831 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:27:13.868 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:27:14.744 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:27:15.973 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:27:15.977 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:27:15.977 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:27:15.977 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:27:15.978 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:27:15.978 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:27:15.978 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:27:15.978 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5cd72f6f
09:27:16.900 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
09:27:17.258 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:27:17.263 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.711 seconds (JVM running for 5.129)
09:27:21.749 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:37:26.727 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Error][验证码错误]
09:37:38.274 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Success][登录成功]
09:42:05.610 [schedule-pool-2] INFO  sys-user - [run,55] - [**********]内网IP[admin][Success][登录成功]
09:45:20.660 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
09:45:20.685 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
09:45:20.685 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
09:45:20.685 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
09:45:20.686 [Thread-13] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
09:45:20.687 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:45:20.689 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:45:21.022 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
09:45:21.023 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:45:21.473 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
09:45:21.473 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:45:21.473 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:45:21.482 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:45:22.504 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:45:23.623 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:45:23.624 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:45:23.624 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:45:23.624 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:45:23.624 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:45:23.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:45:23.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:45:23.624 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3c64bb0c
09:45:24.551 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
09:45:24.830 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:45:24.833 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.839 seconds (JVM running for 1092.694)
09:45:58.043 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:11:32.315 [Thread-22] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:11:32.335 [Thread-22] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:11:32.335 [Thread-22] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:11:32.336 [Thread-22] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:11:32.336 [Thread-22] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:11:32.340 [Thread-22] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
10:11:32.341 [Thread-22] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
10:11:32.755 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:11:32.755 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:11:33.140 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:11:33.140 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:11:33.140 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:11:33.144 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:11:33.717 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
10:11:34.536 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:11:34.536 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:11:34.536 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:11:34.536 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:11:34.536 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:11:34.536 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:11:34.536 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:11:34.536 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@67f61cfe
10:11:35.123 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:11:35.314 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:11:35.316 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.578 seconds (JVM running for 2663.145)
10:15:24.923 [Thread-26] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:15:24.940 [Thread-26] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:15:24.940 [Thread-26] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:15:24.940 [Thread-26] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:15:24.941 [Thread-26] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:15:24.943 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
10:15:24.945 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
10:15:25.344 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:15:25.345 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:15:25.789 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:15:25.789 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:15:25.789 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:15:25.792 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:15:26.452 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
10:15:27.142 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:15:27.143 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:15:27.143 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:15:27.143 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:15:27.143 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:15:27.143 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:15:27.143 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:15:27.143 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2ebc5621
10:15:27.720 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:15:27.903 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:15:27.905 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.596 seconds (JVM running for 2895.732)
10:15:50.662 [Thread-30] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:15:50.681 [Thread-30] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:15:50.682 [Thread-30] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:15:50.682 [Thread-30] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:15:50.682 [Thread-30] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:15:50.685 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
10:15:50.688 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
10:15:51.101 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:15:51.101 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:15:51.443 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:15:51.443 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:15:51.443 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:15:51.449 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:15:52.036 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
10:15:52.788 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:15:52.789 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:15:52.789 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:15:52.789 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:15:52.789 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:15:52.789 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:15:52.789 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:15:52.789 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@26450c3a
10:15:54.103 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:15:54.358 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:15:54.360 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.282 seconds (JVM running for 2922.186)
10:16:18.065 [Thread-34] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:16:18.103 [Thread-34] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:16:18.104 [Thread-34] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:16:18.104 [Thread-34] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:16:18.104 [Thread-34] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:16:18.109 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
10:16:18.111 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
10:16:18.516 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:16:18.516 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:16:18.877 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:16:18.878 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:16:18.878 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:16:18.883 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:16:19.421 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-6} inited
10:16:20.086 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:16:20.087 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:16:20.087 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:16:20.087 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:16:20.087 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:16:20.087 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:16:20.087 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:16:20.087 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2265f341
10:16:20.665 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:16:20.848 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:16:20.850 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.353 seconds (JVM running for 2948.676)
10:17:03.177 [Thread-38] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:17:03.198 [Thread-38] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:17:03.198 [Thread-38] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:17:03.199 [Thread-38] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:17:03.201 [Thread-38] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:17:03.205 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-6} closing ...
10:17:03.211 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-6} closed
10:17:03.789 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:17:03.789 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:17:04.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:17:04.298 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:17:04.298 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:17:04.304 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:17:04.878 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-7} inited
10:17:05.569 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:17:05.569 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:17:05.569 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:17:05.569 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:17:05.569 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:17:05.569 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:17:05.570 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:17:05.570 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7f641359
10:17:06.206 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:17:06.381 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:17:06.383 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.614 seconds (JVM running for 2994.209)
10:17:20.964 [Thread-42] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:17:20.983 [Thread-42] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:17:20.983 [Thread-42] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:17:20.983 [Thread-42] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:17:20.984 [Thread-42] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:17:20.986 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
10:17:20.989 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
10:17:21.439 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:17:21.440 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:17:21.807 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:17:21.807 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:17:21.807 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:17:21.810 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:17:22.373 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
10:17:23.082 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:17:23.082 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:17:23.082 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:17:23.082 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:17:23.082 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:17:23.082 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:17:23.083 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:17:23.083 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3009bf27
10:17:23.705 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:17:24.413 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:17:24.418 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.998 seconds (JVM running for 3012.243)
10:20:15.758 [Thread-46] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:20:15.772 [Thread-46] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:20:15.772 [Thread-46] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:20:15.772 [Thread-46] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:20:15.772 [Thread-46] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:20:15.775 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
10:20:15.778 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
10:20:16.147 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 24873 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:20:16.147 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:20:16.485 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:20:16.485 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:20:16.485 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:20:16.488 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:20:17.173 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
10:20:17.877 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:20:17.878 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:20:17.878 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:20:17.878 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:20:17.878 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:20:17.878 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:20:17.878 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:20:17.878 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1f2a5c0d
10:20:18.571 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:20:18.893 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:20:18.895 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.762 seconds (JVM running for 3186.719)
10:21:08.297 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:21:08.316 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:21:08.316 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:21:08.317 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:21:08.317 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:21:08.320 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
10:21:08.322 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
10:21:10.685 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28365 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:21:10.686 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:21:10.688 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:21:11.607 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:21:11.608 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:21:11.608 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:21:11.639 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:21:12.526 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:21:13.657 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:21:13.664 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:21:13.664 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:21:13.665 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:21:13.665 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:21:13.665 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:21:13.665 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:21:13.665 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@31ef06cf
10:21:14.609 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:21:14.936 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:21:14.941 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.417 seconds (JVM running for 4.726)
10:21:37.559 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:28:02.570 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:28:02.601 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:28:02.602 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:28:02.602 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:28:02.602 [Thread-13] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:28:02.609 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:28:02.612 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:28:02.901 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28365 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:28:02.901 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:28:03.349 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:28:03.349 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:28:03.349 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:28:03.353 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:28:03.969 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:28:04.952 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:28:04.953 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:28:04.953 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:28:04.953 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:28:04.953 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:28:04.953 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:28:04.954 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:28:04.954 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1158669d
10:28:05.648 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:28:05.912 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:28:05.914 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.043 seconds (JVM running for 415.695)
10:28:25.314 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:28:25.327 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:28:25.327 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:28:25.327 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:28:25.328 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:28:25.330 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
10:28:25.338 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
10:28:26.890 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28600 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:28:26.892 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:28:26.895 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:28:27.811 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:28:27.812 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:28:27.812 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:28:27.840 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:28:28.796 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:28:29.868 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:28:29.872 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:28:29.872 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:28:29.872 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:28:29.872 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:28:29.872 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:28:29.872 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:28:29.872 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@553d6704
10:28:30.592 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:28:30.915 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:28:30.919 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.33 seconds (JVM running for 4.716)
10:28:34.583 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:29:28.000 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:29:28.017 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:29:28.017 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:29:28.017 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:29:28.017 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:29:28.019 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:29:28.021 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:29:30.620 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28658 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:29:30.621 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:29:30.622 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:29:31.634 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:29:31.635 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:29:31.635 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:29:31.670 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:29:32.794 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:29:33.941 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:29:33.956 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:29:33.957 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:29:33.958 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:29:33.958 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:29:33.959 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:29:33.959 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:29:33.959 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5b69b766
10:29:34.820 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:29:35.155 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:29:35.160 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.794 seconds (JVM running for 5.235)
10:29:39.784 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:29:40.742 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:29:40.764 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:29:40.764 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:29:40.764 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:29:40.764 [Thread-13] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:29:40.767 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:29:40.772 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:29:41.020 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28658 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:29:41.020 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:29:41.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:29:41.298 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:29:41.299 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:29:41.302 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:29:42.005 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:29:42.625 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:29:42.626 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:29:42.626 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:29:42.626 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:29:42.626 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:29:42.626 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:29:42.626 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:29:42.626 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@53b4a5eb
10:29:43.126 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:29:43.329 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:29:43.331 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.331 seconds (JVM running for 13.407)
10:29:47.371 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:30:08.821 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Success][登录成功]
10:32:48.598 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:32:48.616 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:32:48.617 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:32:48.618 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:32:48.618 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:32:48.620 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
10:32:48.622 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
10:32:51.398 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28855 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:32:51.398 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:32:51.399 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:32:52.372 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:32:52.373 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:32:52.373 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:32:52.404 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:32:53.208 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:32:54.272 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:32:54.276 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:32:54.276 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:32:54.276 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:32:54.276 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:32:54.276 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:32:54.276 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:32:54.277 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@292cc9cd
10:32:55.081 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:32:55.411 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:32:55.416 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.438 seconds (JVM running for 4.918)
10:32:58.820 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:35:19.811 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:35:19.830 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:35:19.830 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:35:19.830 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:35:19.830 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:35:19.833 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:35:19.836 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:35:22.416 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 28951 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:35:22.417 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:35:22.417 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:35:23.295 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:35:23.295 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:35:23.295 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:35:23.325 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:35:24.368 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:35:25.399 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:35:25.402 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:35:25.403 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:35:25.403 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:35:25.403 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:35:25.403 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:35:25.403 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:35:25.403 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@744e4957
10:35:26.132 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:35:26.453 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:35:26.458 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.279 seconds (JVM running for 4.793)
10:35:29.266 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:37:22.961 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:37:22.976 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:37:22.976 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:37:22.976 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:37:22.976 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:37:22.978 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:37:22.980 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:37:25.598 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29053 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:37:25.599 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:37:25.599 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:37:26.541 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:37:26.541 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:37:26.541 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:37:26.573 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:37:27.648 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:37:28.798 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:37:28.803 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:37:28.803 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:37:28.804 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:37:28.804 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:37:28.804 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:37:28.804 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:37:28.804 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7e103726
10:37:29.569 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:37:29.958 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:37:29.963 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.508 seconds (JVM running for 6.058)
10:37:38.205 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:37:57.233 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:37:57.574 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:37:57.574 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:37:57.574 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:37:57.574 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:37:57.577 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:37:57.581 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:37:59.827 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29080 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:37:59.828 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:37:59.831 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:38:00.800 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:38:00.801 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:38:00.801 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:38:00.836 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:38:01.725 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:38:03.321 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:38:03.327 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:38:03.327 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:38:03.327 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:38:03.328 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:38:03.328 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:38:03.328 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:38:03.328 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29ba6997
10:38:04.341 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:38:05.026 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:38:05.038 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 5.365 seconds (JVM running for 6.317)
10:38:17.516 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:38:17.530 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:38:17.530 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:38:17.530 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:38:17.530 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:38:17.532 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:38:17.535 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:38:19.990 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29120 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:38:19.991 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:38:19.991 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:38:21.041 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:38:21.043 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:38:21.043 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:38:21.133 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:38:22.232 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:38:23.374 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:38:23.378 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:38:23.378 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:38:23.378 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:38:23.378 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:38:23.378 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:38:23.378 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:38:23.379 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@39e004d5
10:38:24.247 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:38:24.589 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:38:24.593 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.804 seconds (JVM running for 5.291)
10:38:26.791 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:40:30.063 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:40:30.082 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:40:30.082 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:40:30.082 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:40:30.083 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:40:30.085 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:40:30.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:42:35.185 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29302 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:42:35.186 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:42:35.187 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:42:36.143 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:42:36.144 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:42:36.144 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:42:36.176 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:42:37.017 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:42:38.074 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:42:38.078 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:42:38.078 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:42:38.078 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:42:38.078 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:42:38.078 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:42:38.079 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:42:38.079 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@16157563
10:42:38.864 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:42:39.201 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:42:39.205 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.251 seconds (JVM running for 4.857)
10:42:40.852 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:44:45.063 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:44:45.086 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:44:45.086 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:44:45.086 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:44:45.086 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:44:45.089 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
10:44:45.093 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
10:46:36.877 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29491 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
10:46:36.877 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:46:36.878 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:46:37.877 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
10:46:37.877 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:46:37.877 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:46:37.908 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:46:38.751 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:46:40.094 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:46:40.098 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:46:40.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:46:40.099 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:46:40.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:46:40.099 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:46:40.099 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:46:40.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@487e96f2
10:46:40.854 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
10:46:41.256 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:46:41.264 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.577 seconds (JVM running for 5.044)
10:48:54.499 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:51:18.708 [schedule-pool-1] INFO  sys-user - [run,55] - [**********]内网IP[admin][Success][登录成功]
11:10:10.600 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:10:10.646 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:10:10.646 [Thread-13] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:10:10.647 [Thread-13] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:10:10.647 [Thread-13] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:10:10.650 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
11:10:10.654 [Thread-13] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
11:10:11.019 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29491 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
11:10:11.020 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:10:11.637 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
11:10:11.637 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:10:11.637 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:10:11.643 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:10:12.840 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:10:13.600 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:10:13.601 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:10:13.601 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:10:13.601 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:10:13.601 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:10:13.601 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:10:13.601 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:10:13.601 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51ed34af
11:10:14.190 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
11:10:14.394 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:10:14.396 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.415 seconds (JVM running for 1418.183)
11:10:15.613 [Thread-22] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:10:15.632 [Thread-22] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:10:15.632 [Thread-22] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:10:15.632 [Thread-22] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:10:15.632 [Thread-22] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:10:15.635 [Thread-22] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
11:10:15.638 [Thread-22] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
11:10:16.007 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29491 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
11:10:16.007 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:10:16.276 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
11:10:16.276 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:10:16.276 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:10:16.279 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:10:17.668 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
11:10:18.325 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:10:18.326 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:10:18.326 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:10:18.326 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:10:18.326 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:10:18.326 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:10:18.326 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:10:18.326 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7f41e8b5
11:10:18.847 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
11:10:19.046 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:10:19.048 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.062 seconds (JVM running for 1422.835)
11:10:22.529 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:11:51.571 [Thread-26] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:11:51.608 [Thread-26] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:11:51.608 [Thread-26] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:11:51.608 [Thread-26] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:11:51.609 [Thread-26] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:11:51.612 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
11:11:51.620 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
11:11:51.973 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29491 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
11:11:51.973 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:11:52.341 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
11:11:52.341 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:11:52.341 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:11:52.344 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:11:52.897 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
11:11:53.778 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:11:53.778 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:11:53.778 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:11:53.778 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:11:53.778 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:11:53.778 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:11:53.778 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:11:53.778 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@501b2f2c
11:11:54.355 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
11:11:54.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:11:54.586 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.632 seconds (JVM running for 1518.373)
11:11:55.861 [Thread-30] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:11:55.959 [Thread-30] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:11:55.959 [Thread-30] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:11:55.959 [Thread-30] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:11:55.960 [Thread-30] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:11:55.963 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
11:11:55.966 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
11:11:56.470 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 29491 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
11:11:56.470 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:11:56.968 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
11:11:56.968 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:11:56.968 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:11:56.972 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:11:57.546 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
11:11:58.206 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:11:58.207 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:11:58.207 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:11:58.207 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:11:58.207 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:11:58.207 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:11:58.207 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:11:58.207 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4f2e3447
11:11:58.728 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
11:11:58.934 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:11:58.936 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.489 seconds (JVM running for 1522.723)
11:13:52.212 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:13:52.288 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:13:52.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:13:52.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:13:52.289 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:13:52.296 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
11:13:52.299 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
11:13:54.320 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 30525 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
11:13:54.323 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:13:54.325 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:13:55.920 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
11:13:55.921 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:13:55.921 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:13:55.968 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:13:57.397 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:13:58.547 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:13:58.551 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:13:58.551 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:13:58.552 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:13:58.552 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:13:58.552 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:13:58.552 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:13:58.552 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@60f32375
11:13:59.320 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
11:13:59.645 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:13:59.649 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 5.607 seconds (JVM running for 6.063)
11:14:03.641 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:19:40.399 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:19:40.443 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:19:40.444 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:19:40.444 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:19:40.444 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:19:40.451 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
11:19:40.456 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:21:57.381 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 36604 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:21:57.382 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:21:57.383 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:21:58.330 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:21:58.330 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:21:58.331 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:21:58.364 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:21:59.138 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:22:00.229 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:22:00.232 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:22:00.232 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:22:00.233 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:22:00.233 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:22:00.233 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:22:00.233 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:22:00.233 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7a783bbc
14:22:00.971 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:22:01.289 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:22:01.294 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.066 seconds (JVM running for 4.362)
14:22:28.384 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:22:43.429 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Success][登录成功]
14:24:02.578 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:24:02.589 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:24:02.590 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:24:02.590 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:24:02.590 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:24:02.592 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:24:02.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:30:56.143 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37253 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:30:56.144 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:30:56.144 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:28.544 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37359 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:34:28.548 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:34:28.558 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:30.095 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:34:30.096 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:34:30.096 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:34:30.169 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:34:31.491 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:34:32.570 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:34:32.575 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:34:32.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:34:32.575 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:34:32.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:34:32.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:34:32.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:34:32.576 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6cd73f99
14:34:33.270 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:34:33.587 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:34:33.591 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 5.221 seconds (JVM running for 5.646)
14:35:07.569 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:35:07.668 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:35:07.668 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:35:07.668 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:35:07.668 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:35:07.671 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:35:07.674 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:35:09.448 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37405 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:35:09.449 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:35:09.449 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:35:10.326 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:35:10.326 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:35:10.326 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:35:10.358 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:35:11.448 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:35:12.460 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:35:12.463 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:35:12.464 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:35:12.464 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:35:12.464 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:35:12.464 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:35:12.464 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:35:12.464 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2e972942
14:35:13.172 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:35:13.496 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:35:13.500 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.187 seconds (JVM running for 4.479)
14:35:41.967 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:35:46.568 [schedule-pool-1] INFO  sys-user - [run,55] - [**********]内网IP[admin][Success][登录成功]
14:39:11.864 [Thread-12] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:39:11.885 [Thread-12] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:39:11.885 [Thread-12] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:39:11.885 [Thread-12] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:39:11.886 [Thread-12] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:39:11.887 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:39:11.891 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:39:12.172 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37405 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:39:12.172 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:39:12.528 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:39:12.529 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:39:12.529 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:39:12.532 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:39:13.090 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:39:13.782 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:39:13.783 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:39:13.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:39:13.783 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:39:13.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:39:13.783 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:39:13.783 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:39:13.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7bfd6965
14:39:14.304 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:39:14.501 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:39:14.503 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.352 seconds (JVM running for 245.481)
14:40:08.084 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:40:10.707 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Success][登录成功]
14:46:21.335 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:46:21.345 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:46:21.346 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:46:21.346 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:46:21.346 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:46:21.347 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:46:21.348 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:46:23.720 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37765 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:46:23.721 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:46:23.721 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:46:24.541 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:46:24.542 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:46:24.542 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:46:24.570 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:46:25.348 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:46:26.357 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:46:26.360 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:46:26.360 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:46:26.361 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:46:26.361 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:46:26.361 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:46:26.361 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:46:26.361 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@599c5d39
14:46:27.059 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:46:27.374 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:46:27.378 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.793 seconds (JVM running for 4.068)
14:48:08.530 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:48:09.357 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzMyODksImV4cCI6MTc0ODk2MjA4OX0.6johRryu26LDYMr644JPKQYXAFo2PBsRu6Y-JX-Hvc0","uuid":"93f780ed-8e26-4adf-aaef-279d1fcc2377","message":"认证通过"}
14:48:09.362 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzMyODksImV4cCI6MTc0ODk2MjA4OX0.6johRryu26LDYMr644JPKQYXAFo2PBsRu6Y-JX-Hvc0
14:48:09.363 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
14:48:10.200 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"FAIL","code":0}
14:48:10.201 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,767] - 短信发送响应: {"id":null,"message":"FAIL","code":0}
14:48:10.201 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18775356868, 内容: 有新的应急事件：时间（2025-06-01 17:25:11），地点（G15沈海高速公路上海段K1234+500米处），事件描述（因雨天路滑，发生5车连环相撞事故，现场有人员被困）。请前往系统查看。
14:53:21.956 [Thread-11] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:53:21.997 [Thread-11] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:53:21.997 [Thread-11] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:53:21.997 [Thread-11] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:53:21.997 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:53:22.001 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:53:22.006 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:53:22.325 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37765 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:53:22.326 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:53:22.792 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:53:22.801 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:53:22.812 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:53:22.816 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:53:23.467 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:53:24.236 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:53:24.237 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:53:24.237 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:53:24.237 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:53:24.237 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:53:24.237 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:53:24.237 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:53:24.237 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@57ff04fd
14:53:24.798 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:53:25.005 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:53:25.007 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.707 seconds (JVM running for 421.684)
14:53:26.241 [Thread-19] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:53:26.258 [Thread-19] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:53:26.259 [Thread-19] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:53:26.259 [Thread-19] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:53:26.259 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:53:26.262 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:53:26.264 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:53:26.588 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37765 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:53:26.588 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:53:26.856 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:53:26.856 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:53:26.856 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:53:26.860 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:53:27.385 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
14:53:27.931 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:53:27.931 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:53:27.931 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:53:27.931 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:53:27.931 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:53:27.931 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:53:27.931 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:53:27.932 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7f2f87a0
14:53:28.441 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:53:28.633 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:53:28.635 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.065 seconds (JVM running for 425.311)
14:54:07.848 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:13.479 [Thread-23] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:54:13.499 [Thread-23] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:54:13.499 [Thread-23] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:54:13.500 [Thread-23] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:54:13.500 [Thread-23] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:54:13.503 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
14:54:13.505 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
14:54:13.902 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37765 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
14:54:13.902 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:54:14.205 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
14:54:14.205 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:54:14.205 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:54:14.208 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:54:14.761 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
14:54:15.391 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:54:15.392 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:54:15.392 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:54:15.392 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:54:15.392 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:54:15.392 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:54:15.392 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:54:15.392 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29c831fd
14:54:15.925 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
14:54:16.040 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:16.118 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:54:16.120 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.234 seconds (JVM running for 472.796)
15:01:20.668 [Thread-27] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:01:20.693 [Thread-27] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:01:20.694 [Thread-27] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:01:20.694 [Thread-27] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:01:20.694 [Thread-27] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:01:20.696 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
15:01:20.698 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
15:01:21.032 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 37765 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:01:21.032 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:01:21.355 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:01:21.355 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:01:21.355 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:01:21.359 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:01:21.964 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
15:01:22.616 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:01:22.617 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:01:22.617 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:01:22.617 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:01:22.617 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:01:22.617 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:01:22.617 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:01:22.617 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1d73b067
15:01:23.194 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:01:23.396 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:01:23.398 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.382 seconds (JVM running for 900.071)
15:01:57.108 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:02:07.682 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:02:07.692 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:02:07.692 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:02:07.692 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:02:07.692 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:02:07.694 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
15:02:07.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
15:02:09.280 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 38330 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:02:09.281 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:02:09.281 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:02:10.367 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:02:10.368 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:02:10.368 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:02:10.474 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:02:11.327 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:02:12.333 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:02:12.337 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:02:12.337 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:02:12.337 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:02:12.338 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:02:12.338 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:02:12.338 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:02:12.338 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@471f0912
15:02:13.068 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:02:13.389 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:02:13.394 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.435 seconds (JVM running for 4.762)
15:02:22.122 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:02:22.389 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18775356868，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡。请前往系统查看。"}
15:02:22.539 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzQxNDIsImV4cCI6MTc0ODk2Mjk0Mn0.EkDUDZHfdsjN7Aolr7JHiYZF3K56S0QIRQ8v2nIkVRw","uuid":"a8fecafb-28a3-44a8-b17d-5039c8e4e4d8","message":"认证通过"}
15:02:22.541 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzQxNDIsImV4cCI6MTc0ODk2Mjk0Mn0.EkDUDZHfdsjN7Aolr7JHiYZF3K56S0QIRQ8v2nIkVRw
15:02:22.541 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:02:23.211 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:02:23.212 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,848] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:02:23.212 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18775356868, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡。请前往系统查看。"}
15:07:01.181 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:07:01.194 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:07:01.194 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:07:01.195 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:07:01.195 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:07:01.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:07:01.200 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:07:03.656 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 38519 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:07:03.657 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:07:03.659 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:04.481 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:07:04.481 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:07:04.482 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:07:04.509 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:07:05.468 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:07:06.463 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:07:06.466 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:07:06.466 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:07:06.467 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:07:06.467 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:07:06.467 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:07:06.467 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:07:06.467 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2471c6b0
15:07:07.181 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:07:07.496 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:07:07.500 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.985 seconds (JVM running for 4.309)
15:07:45.875 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:07:46.135 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18775356868，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:07:46.255 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzQ0NjYsImV4cCI6MTc0ODk2MzI2Nn0.7IZss7_GJoWOSMfIxgX32XoxSMAxIAdcFC5BhzEmSoQ","uuid":"e8d93f5c-ae10-41e7-ae84-0a3829b87b80","message":"认证通过"}
15:07:46.257 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzQ0NjYsImV4cCI6MTc0ODk2MzI2Nn0.7IZss7_GJoWOSMfIxgX32XoxSMAxIAdcFC5BhzEmSoQ
15:07:46.257 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:07:46.970 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:07:46.971 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,846] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:07:46.971 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18775356868, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:18:07.639 [http-nio-8380-exec-7] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18775356868，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:18:08.762 [http-nio-8380-exec-7] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzUwODksImV4cCI6MTc0ODk2Mzg4OX0.m96zbZe_2Cv5imS5NzCZ5lBvxMXFNey9GU-OYLb9ok4","uuid":"d5b71edc-f582-4eda-a751-0201f1832d28","message":"认证通过"}
15:18:08.766 [http-nio-8380-exec-7] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzUwODksImV4cCI6MTc0ODk2Mzg4OX0.m96zbZe_2Cv5imS5NzCZ5lBvxMXFNey9GU-OYLb9ok4
15:18:08.767 [http-nio-8380-exec-7] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:18:09.332 [http-nio-8380-exec-7] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:18:09.334 [http-nio-8380-exec-7] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,846] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:18:09.334 [http-nio-8380-exec-7] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18775356868, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:22:28.038 [http-nio-8380-exec-20] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18977998561，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:22:28.080 [http-nio-8380-exec-20] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzUzNDgsImV4cCI6MTc0ODk2NDE0OH0.uTmynyChGqIcvSg0jRuGeeBK2k610cy6vatz4tU0MbQ","uuid":"bd65bb34-8273-4b08-aa7e-b6481b59c877","message":"认证通过"}
15:22:28.082 [http-nio-8380-exec-20] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzUzNDgsImV4cCI6MTc0ODk2NDE0OH0.uTmynyChGqIcvSg0jRuGeeBK2k610cy6vatz4tU0MbQ
15:22:28.082 [http-nio-8380-exec-20] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:22:28.733 [http-nio-8380-exec-20] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:22:28.734 [http-nio-8380-exec-20] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,846] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:22:28.734 [http-nio-8380-exec-20] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18977998561, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:25:22.175 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:25:22.196 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:25:22.196 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:25:22.196 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:25:22.196 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:25:22.199 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:25:22.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:25:24.469 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 39106 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:25:24.470 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:25:24.472 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:25:25.294 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:25:25.295 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:25:25.295 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:25:25.322 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:25:26.417 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:25:27.440 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:25:27.443 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:25:27.443 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:25:27.443 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:25:27.444 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:25:27.444 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:25:27.444 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:25:27.444 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4202a5f7
15:25:28.146 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:25:28.458 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:25:28.462 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.133 seconds (JVM running for 4.385)
15:25:39.506 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:41.741 [http-nio-8380-exec-4] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18977998561，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:25:41.893 [http-nio-8380-exec-4] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzU1NDIsImV4cCI6MTc0ODk2NDM0Mn0.DlN8MuyYPBKnWa1VSQ--jk5yHhz5AELMqj_4uQxcDhU","uuid":"af94be04-a4b6-4101-b549-e265a4c242ec","message":"认证通过"}
15:25:41.903 [http-nio-8380-exec-4] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzU1NDIsImV4cCI6MTc0ODk2NDM0Mn0.DlN8MuyYPBKnWa1VSQ--jk5yHhz5AELMqj_4uQxcDhU
15:25:41.904 [http-nio-8380-exec-4] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:25:42.628 [http-nio-8380-exec-4] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:25:42.628 [http-nio-8380-exec-4] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,847] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:25:42.628 [http-nio-8380-exec-4] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18977998561, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:29:42.289 [http-nio-8380-exec-25] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,692] - 手机号码: 18775356868，短信内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:29:42.345 [http-nio-8380-exec-25] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzU3ODIsImV4cCI6MTc0ODk2NDU4Mn0.sXHz5NXiaTxefvw26FBymjL7gDrHpI_824toauG5kMw","uuid":"4ec7f216-16b8-47b4-a506-68a16aa939cb","message":"认证通过"}
15:29:42.349 [http-nio-8380-exec-25] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzU3ODIsImV4cCI6MTc0ODk2NDU4Mn0.sXHz5NXiaTxefvw26FBymjL7gDrHpI_824toauG5kMw
15:29:42.349 [http-nio-8380-exec-25] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:29:43.048 [http-nio-8380-exec-25] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:29:43.050 [http-nio-8380-exec-25] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,847] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:29:43.050 [http-nio-8380-exec-25] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,698] - 短信发送成功，手机号: 18775356868, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:30:18.649 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:30:18.699 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:30:18.699 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:30:18.699 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:30:18.700 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:30:18.702 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:30:18.704 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:30:21.127 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 39274 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:30:21.128 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:30:21.129 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:30:21.940 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:30:21.940 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:30:21.940 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:30:21.969 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:30:22.954 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:30:23.974 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:30:23.977 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:30:23.977 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:30:23.977 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:30:23.978 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:30:23.978 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:30:23.978 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:30:23.978 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3206015f
15:30:24.755 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:30:25.083 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:30:25.087 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.104 seconds (JVM running for 4.354)
15:30:47.649 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:33:11.028 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:33:11.052 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:33:11.052 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:33:11.052 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:33:11.052 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:33:11.055 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:33:11.061 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:33:13.437 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 39451 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:33:13.439 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:33:13.441 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:33:14.579 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:33:14.580 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:33:14.580 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:33:14.610 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:33:15.388 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:33:16.412 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:33:16.416 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:33:16.416 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:33:16.416 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:33:16.416 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:33:16.416 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:33:16.416 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:33:16.417 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@17b2d9e5
15:33:17.122 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:33:17.452 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:33:17.456 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.234 seconds (JVM running for 4.481)
15:33:37.649 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:33:38.039 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzYwMTgsImV4cCI6MTc0ODk2NDgxOH0.fh_fw2tEkmV_B1WeZ8b0QB7cIDLNQhGmt4PY0uRkD4Y","uuid":"bbeb3e4e-5409-4a01-acb9-0fd9433c6e65","message":"认证通过"}
15:33:38.042 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzYwMTgsImV4cCI6MTc0ODk2NDgxOH0.fh_fw2tEkmV_B1WeZ8b0QB7cIDLNQhGmt4PY0uRkD4Y
15:33:38.042 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:33:38.668 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:33:38.669 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,846] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:33:38.669 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,697] - 短信发送成功，手机号: 18977998561, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:34:46.834 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:34:46.845 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:34:46.845 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:34:46.845 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:34:46.846 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:34:46.848 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:34:46.850 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:34:49.100 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 39537 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:34:49.101 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:34:49.101 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:34:49.923 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:34:49.924 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:34:49.924 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:34:49.952 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:34:50.800 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:34:51.958 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:34:51.962 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:34:51.962 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:34:51.962 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:34:51.962 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:34:51.962 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:34:51.963 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:34:51.963 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2b2388eb
15:34:52.686 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:34:53.045 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:34:53.049 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.084 seconds (JVM running for 4.335)
15:34:59.733 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:35:00.149 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzYxMDAsImV4cCI6MTc0ODk2NDkwMH0.Btkx2eGzKHuUN_mw_lMdvHWBSUYeat4rCKe1Jj8w5ZE","uuid":"033afef7-700d-4943-8866-31c24e114ee2","message":"认证通过"}
15:35:00.152 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzYxMDAsImV4cCI6MTc0ODk2NDkwMH0.Btkx2eGzKHuUN_mw_lMdvHWBSUYeat4rCKe1Jj8w5ZE
15:35:00.152 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:35:00.850 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:35:00.851 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,846] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:35:00.852 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,697] - 短信发送成功，手机号: 18977998561, 内容: {"date":"2025年06月01日17时25分","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025-06-01 17:25:11恢复通行，目前轻伤3人，重伤1人，无死亡"}
15:40:04.947 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:40:04.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:40:04.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:40:04.960 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:40:04.960 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:40:04.964 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:40:04.966 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:40:07.365 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 40160 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:40:07.366 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:40:07.366 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:40:08.174 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:40:08.175 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:40:08.175 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:40:08.203 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:40:09.283 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:40:10.297 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:40:10.301 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:40:10.301 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:40:10.301 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:40:10.301 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:40:10.302 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:40:10.302 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:40:10.302 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@363b153e
15:40:11.022 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:40:11.331 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:40:11.335 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.11 seconds (JVM running for 4.357)
15:41:24.182 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:48:40.330 [Thread-12] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:48:40.344 [Thread-12] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:48:40.345 [Thread-12] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:48:40.345 [Thread-12] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:48:40.345 [Thread-12] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:48:40.347 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
15:48:40.352 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
15:48:40.640 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 40160 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:48:40.640 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:48:41.100 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:48:41.100 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:48:41.100 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:48:41.105 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:48:41.757 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:48:42.734 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:48:42.735 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:48:42.735 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:48:42.735 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:48:42.735 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:48:42.735 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:48:42.735 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:48:42.735 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6faeb849
15:48:43.365 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:48:43.675 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:48:43.677 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.062 seconds (JVM running for 516.703)
15:48:44.783 [Thread-20] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:48:44.798 [Thread-20] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:48:44.798 [Thread-20] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:48:44.798 [Thread-20] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:48:44.798 [Thread-20] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:48:44.800 [Thread-20] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
15:48:44.803 [Thread-20] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
15:48:45.303 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 40160 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:48:45.304 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:48:45.767 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:48:45.767 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:48:45.767 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:48:45.772 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:48:46.411 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
15:48:47.099 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:48:47.100 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:48:47.100 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:48:47.100 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:48:47.100 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:48:47.100 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:48:47.100 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:48:47.100 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@374356d9
15:48:47.730 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:48:47.946 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:48:47.948 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.682 seconds (JVM running for 520.974)
15:49:22.348 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:49:48.023 [Thread-24] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:49:48.036 [Thread-24] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:49:48.037 [Thread-24] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:49:48.037 [Thread-24] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:49:48.037 [Thread-24] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:49:48.039 [Thread-24] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
15:49:48.042 [Thread-24] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
15:49:48.418 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 40160 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:49:48.419 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:49:48.784 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:49:48.784 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:49:48.784 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:49:48.788 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:49:49.441 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
15:49:50.103 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:49:50.104 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:49:50.104 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:49:50.104 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:49:50.104 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:49:50.104 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:49:50.104 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:49:50.104 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@773e24e5
15:49:50.739 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:49:50.947 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:49:50.948 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 2.548 seconds (JVM running for 583.974)
15:49:51.698 [http-nio-8380-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:49:59.320 [Thread-28] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:49:59.337 [Thread-28] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:49:59.337 [Thread-28] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:49:59.337 [Thread-28] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:49:59.337 [Thread-28] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:49:59.340 [Thread-28] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
15:49:59.342 [Thread-28] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
15:49:59.724 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 40160 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:49:59.724 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:50:00.090 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:50:00.090 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:50:00.090 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:50:00.094 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:50:00.966 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
15:50:02.077 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:50:02.078 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:50:02.078 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:50:02.078 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:50:02.078 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:50:02.078 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:50:02.078 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:50:02.078 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@de39627
15:50:02.666 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:50:03.054 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:50:03.056 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 3.351 seconds (JVM running for 596.082)
15:51:03.382 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:51:03.395 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:51:03.395 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:51:03.395 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:51:03.395 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:51:03.402 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
15:51:03.405 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
15:51:05.960 [restartedMain] INFO  c.t.ToccApplication - [logStarting,55] - Starting ToccApplication using Java 1.8.0_441 on leightgdeMacBook-Air.local with PID 41047 (/Users/<USER>/code/tocc-backend/tocc-admin/target/classes started by leightg in /Users/<USER>/code/tocc-backend)
15:51:05.962 [restartedMain] INFO  c.t.ToccApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:51:05.963 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:51:06.867 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8380"]
15:51:06.868 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:51:06.868 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:51:06.900 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:51:08.033 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:51:09.095 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:51:09.099 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:51:09.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:51:09.099 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:51:09.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:51:09.099 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:51:09.099 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:51:09.099 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d3dafd5
15:51:09.889 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8380"]
15:51:10.221 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:51:10.225 [restartedMain] INFO  c.t.ToccApplication - [logStarted,61] - Started ToccApplication in 4.419 seconds (JVM running for 4.724)
15:51:46.469 [http-nio-8380-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:51:47.002 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken,content:{"code":1,"token":"fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzcxMDcsImV4cCI6MTc0ODk2NTkwN30.NJagKS1JFvmTkbWyg9TBEJe8EEO1yR5PinUUcHIRDvo","uuid":"685821a7-8db0-4ed4-89f0-1cde65f9d16c","message":"认证通过"}
15:51:47.005 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:Authorization,value:fb9686ae-8706-46ce-926d-23ddfbc010e9@@@eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiJmYjk2ODZhZS04NzA2LTQ2Y2UtOTI2ZC0yM2RkZmJjMDEwZTkiLCJpYXQiOjE3NDg5MzcxMDcsImV4cCI6MTc0ODk2NTkwN30.NJagKS1JFvmTkbWyg9TBEJe8EEO1yR5PinUUcHIRDvo
15:51:47.005 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,95] - Header=key:AuthorizationType,value:other
15:51:47.729 [http-nio-8380-exec-2] INFO  c.t.u.HttpClientUtils - [post,106] - URL:https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend,content:{"id":null,"message":"SUCCESS","code":1}
15:51:47.730 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [smsSend,865] - 短信发送响应: {"id":null,"message":"SUCCESS","code":1}
15:51:47.731 [http-nio-8380-exec-2] INFO  c.t.s.i.EmergencyEventServiceImpl - [sendSmsToSubmitter,721] - 短信发送成功，手机号: 18977998561, 内容: {"date":"2025年06月01日17时25分11秒","location":"G15沈海高速公路上海段K1234+500米处","content":"发生一起道路交通事故，因雨天路滑，发生5车连环相撞事故，现场有人员被困。该事故影响通行，预计于2025年06月01日17时25分11秒恢复通行，目前轻伤3人，重伤1人，无死亡"}
