# 救援队伍功能API接口文档

## 功能概述

救援队伍管理功能提供了完整的救援队伍信息管理，包括队伍基础信息、物资配置、装备管理等功能。

## 数据库表结构

### 1. 救援队伍主表 (rescue_team)
- 队伍基础信息：名称、编号、地址、经纬度、人数
- 负责人信息：姓名、联系方式
- 管辖单位信息：单位名称、负责人、联系方式
- 队伍属性：类型、专业特长、状态

### 2. 物资关联表 (rescue_team_material)
- 简单的多对多关联关系
- 联合主键：(team_id, material_id)

### 3. 物资表 (em_material)
- 通过 material_type 字段区分：0-应急物资，1-应急装备

## API接口列表

### 1. 基础CRUD接口

#### 1.1 查询救援队伍列表
```
GET /rescue/team/list
```

**参数：**
- `pageNum`: 页码
- `pageSize`: 每页大小
- `teamName`: 队伍名称（模糊查询）
- `teamCode`: 队伍编号
- `teamType`: 队伍类型
- `jurisdictionUnit`: 管辖单位（模糊查询）
- `status`: 状态
- `params[beginTime]`: 开始时间
- `params[endTime]`: 结束时间

**响应示例：**
```json
{
  "total": 10,
  "rows": [
    {
      "id": "team_001",
      "teamName": "市消防救援队",
      "teamCode": "FIRE001",
      "address": "南宁市青秀区民族大道100号",
      "longitude": 108.366543,
      "latitude": 22.817002,
      "teamSize": 50,
      "leaderName": "张三",
      "leaderPhone": "13800138000",
      "jurisdictionUnit": "南宁市消防救援支队",
      "jurisdictionLeader": "李四",
      "jurisdictionPhone": "13900139000",
      "teamType": "1",
      "teamTypeName": "专业救援",
      "specialties": "火灾救援,地震救援",
      "status": 1,
      "statusName": "正常",
      "createTime": "2024-06-02 10:00:00"
    }
  ]
}
```

#### 1.2 获取救援队伍详情
```
GET /rescue/team/{id}
```

**响应包含：**
- 队伍基础信息
- 关联的物资列表 (materials)
- 关联的装备列表 (equipments)

#### 1.3 新增救援队伍（同时创建专属物资和装备）
```
POST /rescue/team
Content-Type: application/json

{
  "teamName": "南宁市消防救援第一队",
  "teamCode": "NNXF001",
  "address": "南宁市青秀区民族大道100号消防大队",
  "longitude": 108.366543,
  "latitude": 22.817002,
  "teamSize": 45,
  "leaderName": "张志强",
  "leaderPhone": "13800138001",
  "jurisdictionUnit": "南宁市消防救援支队",
  "jurisdictionLeader": "李建国",
  "jurisdictionPhone": "13900139001",
  "teamType": "1",
  "specialties": "火灾救援,地震救援,水域救援,高空救援",
  "status": 1,
  "remark": "专业消防救援队伍，配备先进救援设备，24小时待命",
  "materials": [
    {
      "materialName": "医疗急救包",
      "specModel": "标准型",
      "categoryCode": "MEDICAL",
      "quantity": 50,
      "unit": "个",
      "expiryDate": "2025-12-31",
      "remark": "包含基础医疗用品"
    },
    {
      "materialName": "应急食品",
      "specModel": "压缩饼干",
      "categoryCode": "FOOD",
      "quantity": 200,
      "unit": "包",
      "expiryDate": "2025-06-30",
      "remark": "高能量压缩食品"
    }
  ],
  "equipments": [
    {
      "materialName": "消防水枪",
      "specModel": "QZ19/6.5",
      "categoryCode": "FIRE_EQUIP",
      "quantity": 10,
      "unit": "支",
      "remark": "高压消防水枪"
    },
    {
      "materialName": "救生绳索",
      "specModel": "直径12mm",
      "categoryCode": "RESCUE_EQUIP",
      "quantity": 20,
      "unit": "根",
      "remark": "高强度救援绳索，长度50米"
    }
  ]
}
```

#### 1.4 修改救援队伍
```
PUT /rescue/team
Content-Type: application/json
```

#### 1.5 删除救援队伍
```
DELETE /rescue/team/{ids}
```

### 2. 物资管理接口

#### 2.1 查询队伍物资列表
```
GET /rescue/team/{teamId}/materials
```

#### 2.2 查询队伍装备列表
```
GET /rescue/team/{teamId}/equipments
```

#### 2.3 更新队伍物资配置
```
PUT /rescue/team/{teamId}/materials
Content-Type: application/json

["material_001", "material_002", "material_003"]
```

#### 2.4 添加单个物资
```
POST /rescue/team/{teamId}/material/{materialId}
```

#### 2.5 移除单个物资
```
DELETE /rescue/team/{teamId}/material/{materialId}
```

### 3. 辅助接口

#### 3.1 检查队伍编号唯一性
```
GET /rescue/team/checkTeamCodeUnique?teamCode=FIRE001&id=team_001
```

#### 3.2 导出救援队伍数据
```
POST /rescue/team/export
```

## 权限配置

### 权限标识
- `rescue:team:list` - 查询救援队伍列表
- `rescue:team:query` - 查询救援队伍详情
- `rescue:team:add` - 新增救援队伍
- `rescue:team:edit` - 修改救援队伍
- `rescue:team:remove` - 删除救援队伍
- `rescue:team:export` - 导出救援队伍

## 字典配置

### 1. 队伍类型 (rescue_team_type)
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, remark) 
VALUES ('救援队伍类型', 'rescue_team_type', '0', '救援队伍类型字典');

INSERT INTO sys_dict_data (dict_type, dict_label, dict_value, dict_sort, status) VALUES
('rescue_team_type', '专业救援', '1', 1, '0'),
('rescue_team_type', '志愿救援', '2', 2, '0'),
('rescue_team_type', '企业救援', '3', 3, '0'),
('rescue_team_type', '社区救援', '4', 4, '0');
```

### 2. 队伍状态 (rescue_team_status)
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, remark) 
VALUES ('救援队伍状态', 'rescue_team_status', '0', '救援队伍状态字典');

INSERT INTO sys_dict_data (dict_type, dict_label, dict_value, dict_sort, status) VALUES
('rescue_team_status', '正常', '1', 1, '0'),
('rescue_team_status', '停用', '2', 2, '0');
```

## 使用示例

### 1. 创建救援队伍
```javascript
// 创建救援队伍
const teamData = {
  teamName: "市消防救援队",
  teamCode: "FIRE001",
  address: "南宁市青秀区民族大道100号",
  longitude: 108.366543,
  latitude: 22.817002,
  teamSize: 50,
  leaderName: "张三",
  leaderPhone: "13800138000",
  jurisdictionUnit: "南宁市消防救援支队",
  jurisdictionLeader: "李四",
  jurisdictionPhone: "13900139000",
  teamType: "1",
  specialties: "火灾救援,地震救援",
  status: 1,
  materialIds: ["material_001", "material_002"]
};

axios.post('/rescue/team', teamData)
  .then(response => {
    console.log('创建成功:', response.data);
  });
```

### 2. 查询队伍列表
```javascript
// 查询救援队伍列表
axios.get('/rescue/team/list', {
  params: {
    pageNum: 1,
    pageSize: 10,
    teamName: '消防',
    teamType: '1'
  }
}).then(response => {
  console.log('队伍列表:', response.data.rows);
});
```

### 3. 管理队伍物资
```javascript
// 更新队伍物资配置
const materialIds = ["material_001", "material_002", "material_003"];
axios.put(`/rescue/team/${teamId}/materials`, materialIds)
  .then(response => {
    console.log('物资配置更新成功');
  });

// 添加单个物资
axios.post(`/rescue/team/${teamId}/material/${materialId}`)
  .then(response => {
    console.log('物资添加成功');
  });
```

## 注意事项

1. **队伍编号唯一性**：系统会自动检查队伍编号的唯一性
2. **物资关联**：支持批量配置和单个操作两种方式
3. **软删除**：删除队伍时使用软删除，不会物理删除数据
4. **事务处理**：新增、修改、删除操作都使用事务保证数据一致性
5. **权限控制**：所有接口都有相应的权限控制

这个救援队伍功能提供了完整的队伍管理能力，支持队伍信息维护、物资配置、数据导出等功能！
